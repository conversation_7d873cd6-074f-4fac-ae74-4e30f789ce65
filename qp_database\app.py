from src.extractor import StartProcessing
import logging
import os
from pathlib import Path
import google.generativeai as genai
import json
from dotenv import load_dotenv


load_dotenv()

if __name__ == "__main__":
    # Get project root directory
    project_root = Path(__file__).parent

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        filename=project_root / "pdf_processor.log",
        filemode="a"
    )

    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    if not GEMINI_API_KEY:
        raise ValueError("No GEMINI_API_KEY found in .env file")
    genai.configure(api_key=GEMINI_API_KEY)

    # Load JSON config using Path
    try:
        config_path = project_root / "src" / "config" / "config.json"
        if not config_path.exists():
            raise FileNotFoundError(f"Config file not found at: {config_path}")
            
        with open(config_path, "r") as f:
            config = json.load(f)

        # Make paths absolute relative to project root
        input_dir = project_root / config["input_dir"]
        output_excel_dir = project_root / config["output_excel_dir"]
        output_image_dir = project_root / config["output_image_dir"]

        # Create output directories
        output_excel_dir.mkdir(parents=True, exist_ok=True)
        output_image_dir.mkdir(parents=True, exist_ok=True)

        # Convert to strings for compatibility with existing code
        input_dir = str(input_dir)
        output_excel_dir = str(output_excel_dir)
        output_image_dir = str(output_image_dir)
    except Exception as e:
        logging.error(f"Error loading config: {e}")
        raise

    obj = StartProcessing(input_dir, output_excel_dir, output_image_dir)
    obj.extract_all_questions_from_pdf()
