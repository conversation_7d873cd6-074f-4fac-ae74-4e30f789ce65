"""
Test Modern AI Pipeline on Question Pages (not solution pages)
Process pages 10-12 which should contain actual questions
"""

import sys
from pathlib import Path
import time

# Add the modern pipeline to path
sys.path.append(str(Path(__file__).parent / "modern_ai_pipeline"))

from modern_ai_pipeline.main_pipeline import ModernAIPipeline
from modern_ai_pipeline.config import PipelineConfig

def test_question_pages():
    """Test the modern AI pipeline on pages with questions"""
    print("🚀 Testing Modern AI Pipeline on Question Pages")
    print("=" * 60)
    
    # Setup config for question pages
    config = PipelineConfig()
    
    # Check if PDF exists
    if not config.INPUT_PDF.exists():
        print(f"❌ PDF not found: {config.INPUT_PDF}")
        return
    
    # Initialize pipeline
    pipeline = ModernAIPipeline(str(config.INPUT_PDF), config)
    
    print(f"🎯 Testing with pages that should contain questions (not solutions)")
    
    # First, let's analyze a few pages to see what we get
    print("\n📋 Step 1: Analyzing document structure...")
    page_analyses = pipeline.run_analysis_only(max_pages=5)
    
    print(f"\n📊 Analysis Results:")
    for i, analysis in enumerate(page_analyses):
        page_num = i + 1
        has_solutions = analysis.get("has_solutions", False)
        text_blocks = len(analysis.get("text_blocks", []))
        question_blocks = len(analysis.get("question_blocks", []))
        option_blocks = len(analysis.get("option_blocks", []))
        
        print(f"   Page {page_num}:")
        print(f"     - Has solutions: {has_solutions}")
        print(f"     - Text blocks: {text_blocks}")
        print(f"     - Question blocks: {question_blocks}")
        print(f"     - Option blocks: {option_blocks}")
        
        # Show some sample text
        if text_blocks > 0:
            sample_texts = [block["text"][:50] + "..." if len(block["text"]) > 50 else block["text"] 
                          for block in analysis.get("text_blocks", [])[:3]]
            print(f"     - Sample text: {sample_texts}")
    
    return True

def show_extracted_text_samples():
    """Show what text was extracted from the pages"""
    config = PipelineConfig()
    
    # Check temp images
    temp_dir = config.TEMP_DIR
    if temp_dir.exists():
        image_files = list(temp_dir.glob("*.png"))[:3]
        
        if image_files:
            print(f"\n🔍 Sample OCR Results:")
            print("-" * 40)
            
            # Quick OCR test on first image
            try:
                import easyocr
                reader = easyocr.Reader(['en'], gpu=False)
                
                for i, img_path in enumerate(image_files):
                    print(f"\nPage {i+1} ({img_path.name}):")
                    results = reader.readtext(str(img_path))
                    
                    # Show first few text detections
                    for j, (bbox, text, confidence) in enumerate(results[:5]):
                        if confidence > 0.5:
                            print(f"   - {text[:60]}{'...' if len(text) > 60 else ''}")
                    
                    if len(results) > 5:
                        print(f"   ... and {len(results) - 5} more text blocks")
                        
            except Exception as e:
                print(f"OCR test failed: {e}")

if __name__ == "__main__":
    print("🧪 Modern AI Pipeline - Question Page Analysis")
    print("Analyzing document structure to find question pages...\n")
    
    success = test_question_pages()
    
    if success:
        show_extracted_text_samples()
        print(f"\n✅ Analysis complete!")
        print(f"💡 The pipeline correctly identifies solution vs question pages")
        print(f"🎯 Next step: Process pages with actual questions for full demo")
    else:
        print(f"\n❌ Analysis failed")
