"""
Find Question Pages in PDF and Run Full Modern AI Pipeline
Test different page ranges to find actual questions (not solutions)
"""

import sys
from pathlib import Path
import time

# Add the modern pipeline to path
sys.path.append(str(Path(__file__).parent / "modern_ai_pipeline"))

from modern_ai_pipeline.main_pipeline import ModernAIPipeline
from modern_ai_pipeline.config import PipelineConfig

def find_question_pages():
    """Find pages that contain questions (not solutions)"""
    print("🔍 Finding Question Pages in PDF")
    print("=" * 60)
    
    config = PipelineConfig()
    pipeline = ModernAIPipeline(str(config.INPUT_PDF), config)
    
    # Test different page ranges
    test_ranges = [
        (6, 10),   # Pages 6-10
        (11, 15),  # Pages 11-15  
        (16, 20),  # Pages 16-20
    ]
    
    question_pages = []
    
    for start_page, end_page in test_ranges:
        print(f"\n📋 Testing pages {start_page}-{end_page}...")
        
        # Analyze this range
        try:
            # Convert specific page range
            image_paths = pipeline.pdf_converter.convert_pages_to_images(max_pages=end_page)
            
            # Analyze only the pages in our range
            for page_idx in range(start_page-1, min(end_page, len(image_paths))):
                if page_idx < len(image_paths):
                    analysis = pipeline.document_analyzer.analyze_page(image_paths[page_idx])
                    
                    page_num = page_idx + 1
                    has_solutions = analysis.get("has_solutions", False)
                    text_blocks = len(analysis.get("text_blocks", []))
                    question_blocks = len(analysis.get("question_blocks", []))
                    option_blocks = len(analysis.get("option_blocks", []))
                    
                    print(f"   Page {page_num}: Solutions={has_solutions}, Questions={question_blocks}, Options={option_blocks}")
                    
                    # If this page has questions but no solutions, it's a candidate
                    if not has_solutions and question_blocks > 0 and option_blocks > 0:
                        question_pages.append(page_num)
                        print(f"   ✅ Page {page_num} looks like a question page!")
                    
        except Exception as e:
            print(f"   ❌ Error analyzing pages {start_page}-{end_page}: {e}")
    
    return question_pages

def run_full_pipeline_on_questions(start_page=10):
    """Run the full modern AI pipeline starting from a specific page"""
    print(f"\n🚀 Running Full Modern AI Pipeline (starting from page {start_page})")
    print("=" * 60)
    
    # Create custom config
    config = PipelineConfig()
    
    # Modify config to start from a specific page and process 10 pages
    class CustomConfig(PipelineConfig):
        MAX_PAGES = 10  # Process 10 pages
        START_PAGE = start_page
    
    custom_config = CustomConfig()
    
    # Initialize pipeline with custom config
    pipeline = ModernAIPipeline(str(config.INPUT_PDF), custom_config)
    
    # Run the complete pipeline
    start_time = time.time()
    results = pipeline.run_complete_pipeline()
    end_time = time.time()
    
    print("\n" + "=" * 60)
    
    if results["success"]:
        print("✅ Modern AI Pipeline completed successfully!")
        print(f"\n📊 Final Results:")
        print(f"   - Questions extracted: {results['total_questions']}")
        print(f"   - Pages processed: {results['pages_processed']}")
        print(f"   - Questions with images: {results['questions_with_images']}")
        print(f"   - Questions with tables: {results['questions_with_tables']}")
        print(f"   - Processing time: {end_time - start_time:.1f} seconds")
        
        print(f"\n📁 Output files created:")
        for file_type, file_path in results["output_files"].items():
            if Path(file_path).exists():
                size_kb = Path(file_path).stat().st_size / 1024
                print(f"   - {file_type.upper()}: {file_path} ({size_kb:.1f} KB)")
        
        return True
    else:
        print(f"❌ Pipeline failed: {results['error']}")
        return False

def compare_with_traditional():
    """Compare results with traditional method"""
    print(f"\n🔄 Comparison with Traditional Method")
    print("=" * 60)
    
    # Check if traditional results exist
    traditional_files = [
        Path("please/output_first_20/questions.xlsx"),
        Path("please/output_first_20/questions.csv"),
        Path("please/FINAL_RESULTS.md")
    ]
    
    modern_files = [
        Path("please/modern_output/modern_questions.xlsx"),
        Path("please/modern_output/modern_questions.csv"),
        Path("please/modern_output/extraction_summary.md")
    ]
    
    print("📊 File Comparison:")
    print(f"{'Method':<15} {'Excel':<8} {'CSV':<8} {'Report':<8}")
    print("-" * 45)
    
    # Traditional method
    trad_status = [f.exists() for f in traditional_files]
    print(f"{'Traditional':<15} {'✅' if trad_status[0] else '❌':<8} {'✅' if trad_status[1] else '❌':<8} {'✅' if trad_status[2] else '❌':<8}")
    
    # Modern method  
    mod_status = [f.exists() for f in modern_files]
    print(f"{'Modern AI':<15} {'✅' if mod_status[0] else '❌':<8} {'✅' if mod_status[1] else '❌':<8} {'✅' if mod_status[2] else '❌':<8}")
    
    # If both exist, compare question counts
    if trad_status[1] and mod_status[1]:
        try:
            import pandas as pd
            
            trad_df = pd.read_csv(traditional_files[1])
            mod_df = pd.read_csv(modern_files[1])
            
            print(f"\n📈 Question Count Comparison:")
            print(f"   Traditional method: {len(trad_df)} questions")
            print(f"   Modern AI method:   {len(mod_df)} questions")
            
            if len(mod_df) > 0:
                print(f"\n📋 Sample Modern AI Results:")
                for i in range(min(3, len(mod_df))):
                    q_num = mod_df.iloc[i]['Question_Number']
                    q_text = mod_df.iloc[i]['Question_Text'][:60] + "..."
                    print(f"   Q{q_num}: {q_text}")
                    
        except Exception as e:
            print(f"   ❌ Error comparing files: {e}")

def main():
    """Main function"""
    print("🧪 Modern AI Pipeline - Full Question Extraction")
    print("Finding question pages and running complete extraction...\n")
    
    # Step 1: Find question pages
    question_pages = find_question_pages()
    
    if question_pages:
        print(f"\n✅ Found question pages: {question_pages}")
        start_page = min(question_pages)
        print(f"🎯 Will start extraction from page {start_page}")
        
        # Step 2: Run full pipeline
        success = run_full_pipeline_on_questions(start_page)
        
        if success:
            # Step 3: Compare with traditional method
            compare_with_traditional()
            
            print(f"\n🎉 Modern AI Pipeline Complete!")
            print(f"💡 Check the output files in modern_output/ directory")
        
    else:
        print(f"\n⚠️ No clear question pages found in tested ranges")
        print(f"💡 Running pipeline on pages 10-20 anyway...")
        run_full_pipeline_on_questions(10)

if __name__ == "__main__":
    main()
