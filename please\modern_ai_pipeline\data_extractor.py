"""
Structured Data Extractor for Modern AI Pipeline
Converts document analysis results into clean, structured question data
"""

import logging
import re
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from collections import defaultdict
import numpy as np

try:
    from .config import PipelineConfig
except ImportError:
    from config import PipelineConfig

class StructuredDataExtractor:
    """Extract structured question data from document analysis results"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_questions_from_pages(self, page_analyses: List[Dict]) -> List[Dict]:
        """
        Extract structured questions from multiple page analyses
        
        Args:
            page_analyses: List of page analysis results
            
        Returns:
            List of structured question dictionaries
        """
        self.logger.info(f"Extracting questions from {len(page_analyses)} pages")
        
        all_questions = []
        
        for page_idx, page_data in enumerate(page_analyses):
            # Skip pages with solutions
            if page_data.get("has_solutions", False):
                self.logger.info(f"Skipping page {page_idx + 1} (contains solutions)")
                continue
            
            # Extract questions from this page
            page_questions = self._extract_questions_from_page(page_data, page_idx + 1)
            all_questions.extend(page_questions)
        
        # Sort questions by number
        all_questions.sort(key=lambda q: q.get("question_number", 0))
        
        self.logger.info(f"Extracted {len(all_questions)} questions total")
        return all_questions
    
    def _extract_questions_from_page(self, page_data: Dict, page_num: int) -> List[Dict]:
        """Extract questions from a single page"""
        questions = []
        
        # Get all blocks
        text_blocks = page_data.get("text_blocks", [])
        question_blocks = page_data.get("question_blocks", [])
        option_blocks = page_data.get("option_blocks", [])
        figure_blocks = page_data.get("figure_blocks", [])
        table_blocks = page_data.get("table_blocks", [])
        
        # Group blocks by question number
        question_groups = self._group_blocks_by_question(
            text_blocks, question_blocks, option_blocks, figure_blocks, table_blocks
        )
        
        for q_num, blocks in question_groups.items():
            question = self._build_question_structure(q_num, blocks, page_num)
            if question:
                questions.append(question)
        
        return questions
    
    def _group_blocks_by_question(self, text_blocks: List[Dict], question_blocks: List[Dict], 
                                 option_blocks: List[Dict], figure_blocks: List[Dict],
                                 table_blocks: List[Dict]) -> Dict[int, Dict]:
        """Group all blocks by question number using spatial proximity"""
        question_groups = defaultdict(lambda: {
            "question_text": [],
            "options": [],
            "figures": [],
            "tables": []
        })
        
        # Find question numbers first
        question_numbers = []
        for block in question_blocks:
            if block.get("type") == "question_number":
                q_num = self._extract_question_number(block["text"])
                if q_num:
                    question_numbers.append({
                        "number": q_num,
                        "bbox": block["bbox"],
                        "y_center": (block["bbox"][1] + block["bbox"][3]) / 2
                    })
        
        # Sort by vertical position
        question_numbers.sort(key=lambda x: x["y_center"])
        
        # Assign blocks to questions based on proximity
        for q_info in question_numbers:
            q_num = q_info["number"]
            q_y = q_info["y_center"]
            
            # Find next question's Y position (or page bottom)
            next_q_y = float('inf')
            for other_q in question_numbers:
                if other_q["y_center"] > q_y:
                    next_q_y = other_q["y_center"]
                    break
            
            # Assign blocks in this question's region
            self._assign_blocks_to_question(
                q_num, q_y, next_q_y, question_groups[q_num],
                text_blocks, option_blocks, figure_blocks, table_blocks
            )
        
        return dict(question_groups)
    
    def _extract_question_number(self, text: str) -> Optional[int]:
        """Extract question number from text"""
        # Try different patterns
        patterns = [
            r"^(\d+)\.",  # "1.", "2.", etc.
            r"^Q(\d+)",   # "Q1", "Q2", etc.
            r"(\d+)\)",   # "1)", "2)", etc.
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text.strip())
            if match:
                return int(match.group(1))
        
        return None
    
    def _assign_blocks_to_question(self, q_num: int, q_y: float, next_q_y: float,
                                  question_group: Dict, text_blocks: List[Dict],
                                  option_blocks: List[Dict], figure_blocks: List[Dict],
                                  table_blocks: List[Dict]):
        """Assign blocks to a question based on spatial proximity"""
        
        # Assign text blocks (question text)
        for block in text_blocks:
            block_y = (block["bbox"][1] + block["bbox"][3]) / 2
            if q_y <= block_y < next_q_y and len(block["text"]) > 10:
                # Skip if it's an option or question number
                if not any(pattern in block["text"] for pattern in ["(1)", "(2)", "(3)", "(4)"]):
                    if not re.match(r"^\d+\.", block["text"]):
                        question_group["question_text"].append(block)
        
        # Assign option blocks
        for block in option_blocks:
            block_y = (block["bbox"][1] + block["bbox"][3]) / 2
            if q_y <= block_y < next_q_y:
                question_group["options"].append(block)
        
        # Assign figure blocks
        for block in figure_blocks:
            block_y = (block["bbox"][1] + block["bbox"][3]) / 2
            if q_y <= block_y < next_q_y:
                question_group["figures"].append(block)
        
        # Assign table blocks
        for block in table_blocks:
            block_y = (block["bbox"][1] + block["bbox"][3]) / 2
            if q_y <= block_y < next_q_y:
                question_group["tables"].append(block)
    
    def _build_question_structure(self, q_num: int, blocks: Dict, page_num: int) -> Optional[Dict]:
        """Build final question structure"""
        
        # Combine question text
        question_text_parts = []
        for block in sorted(blocks["question_text"], key=lambda x: x["bbox"][1]):
            text = self._clean_text(block["text"])
            if text:
                question_text_parts.append(text)
        
        question_text = " ".join(question_text_parts)
        
        # Skip if no meaningful question text
        if len(question_text) < 10:
            return None
        
        # Extract options
        options = {}
        for block in blocks["options"]:
            option_num = block.get("option_number")
            if option_num:
                options[option_num] = self._clean_text(block["text"])
        
        # Ensure we have 4 options
        if len(options) < 4:
            return None
        
        # Build question structure
        question = {
            "question_number": q_num,
            "question_text": question_text,
            "option_1": options.get(1, ""),
            "option_2": options.get(2, ""),
            "option_3": options.get(3, ""),
            "option_4": options.get(4, ""),
            "page_number": page_num,
            "has_figure": len(blocks["figures"]) > 0,
            "has_table": len(blocks["tables"]) > 0,
            "figure_bboxes": [fig["bbox"] for fig in blocks["figures"]],
            "table_bboxes": [tab["bbox"] for tab in blocks["tables"]]
        }
        
        return question
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text"""
        if not text:
            return ""
        
        # Remove option markers from option text
        text = re.sub(r"^\([1-4]\)\s*", "", text)
        
        # Remove question number from question text
        text = re.sub(r"^\d+\.\s*", "", text)
        
        # Clean up whitespace
        text = re.sub(r"\s+", " ", text).strip()
        
        # Remove solution patterns
        for pattern in self.config.SOLUTION_PATTERNS:
            text = re.sub(pattern, "", text, flags=re.IGNORECASE)
        
        return text
    
    def save_structured_data(self, questions: List[Dict], output_path: Path):
        """Save structured data to JSON"""
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(questions, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Saved {len(questions)} questions to {output_path}")


def test_extractor():
    """Test the data extractor"""
    config = PipelineConfig()
    extractor = StructuredDataExtractor(config)
    
    # Mock page analysis data for testing
    mock_page_data = {
        "has_solutions": False,
        "text_blocks": [
            {"text": "1. What is the speed of light?", "bbox": [100, 100, 400, 120], "type": "text"},
            {"text": "The speed of light in vacuum is approximately", "bbox": [100, 130, 500, 150], "type": "text"},
            {"text": "(1) 3 × 10^8 m/s", "bbox": [100, 160, 200, 180], "type": "text"},
            {"text": "(2) 3 × 10^6 m/s", "bbox": [100, 190, 200, 210], "type": "text"},
            {"text": "(3) 3 × 10^10 m/s", "bbox": [100, 220, 200, 240], "type": "text"},
            {"text": "(4) 3 × 10^12 m/s", "bbox": [100, 250, 200, 270], "type": "text"},
        ],
        "question_blocks": [
            {"text": "1.", "bbox": [100, 100, 120, 120], "type": "question_number"}
        ],
        "option_blocks": [
            {"text": "(1) 3 × 10^8 m/s", "bbox": [100, 160, 200, 180], "type": "option", "option_number": 1},
            {"text": "(2) 3 × 10^6 m/s", "bbox": [100, 190, 200, 210], "type": "option", "option_number": 2},
            {"text": "(3) 3 × 10^10 m/s", "bbox": [100, 220, 200, 240], "type": "option", "option_number": 3},
            {"text": "(4) 3 × 10^12 m/s", "bbox": [100, 250, 200, 270], "type": "option", "option_number": 4},
        ],
        "figure_blocks": [],
        "table_blocks": []
    }
    
    questions = extractor.extract_questions_from_pages([mock_page_data])
    print(f"Extracted questions: {json.dumps(questions, indent=2)}")


if __name__ == "__main__":
    test_extractor()
