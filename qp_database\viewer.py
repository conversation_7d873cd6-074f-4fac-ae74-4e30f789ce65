#!/usr/bin/env python3
"""
Simple Flask web interface for viewing extracted questions
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import pandas as pd
import os
import json
from datetime import datetime

app = Flask(__name__)

# Configuration
EXCEL_DIR = "data/output/excel"
IMAGES_DIR = "data/output/excel/images"

def get_latest_excel_file():
    """Get the most recent Excel file from the output directory."""
    try:
        excel_files = [f for f in os.listdir(EXCEL_DIR) if f.endswith('.xlsx')]
        if not excel_files:
            return None
        
        # Get the most recent file
        latest_file = max(excel_files, key=lambda f: os.path.getctime(os.path.join(EXCEL_DIR, f)))
        return os.path.join(EXCEL_DIR, latest_file)
    except Exception as e:
        print(f"Error finding Excel file: {e}")
        return None

def load_questions():
    """Load questions from the Excel file."""
    excel_path = get_latest_excel_file()
    if not excel_path:
        return [], "No Excel file found"
    
    try:
        df = pd.read_excel(excel_path)
        questions = df.to_dict('records')
        
        # Process questions to handle images
        for q in questions:
            # Check if question has images
            q['has_image'] = any(ext in str(q['question']) for ext in ['.jpg', '.png', '.jpeg'])
            
            # Extract image filenames from question text
            q['image_files'] = []
            if q['has_image']:
                question_text = str(q['question'])
                words = question_text.split()

                for word in words:
                    # Look for image files (both old format and new format)
                    if any(ext in word for ext in ['.jpg', '.png', '.jpeg']):
                        # Check for new format: Q1_page2_img0_1751979722.jpg
                        if word.startswith('Q') and '_page' in word:
                            q['image_files'].append(word)
                        # Check for old format: content_img_page2_0_0_1751903981.jpg
                        elif word.startswith('content_img_'):
                            q['image_files'].append(word)

                # Clean the question text by removing image filenames
                clean_text = question_text
                for img_file in q['image_files']:
                    clean_text = clean_text.replace(img_file, '').strip()
                q['clean_question'] = clean_text
        
        return questions, None
    except Exception as e:
        return [], f"Error loading Excel file: {e}"

@app.route('/')
def index():
    """Main page showing all questions."""
    questions, error = load_questions()
    
    if error:
        return render_template('error.html', error=error)
    
    # Get statistics
    total_questions = len(questions)
    questions_with_images = sum(1 for q in questions if q['has_image'])
    
    # Get question numbers for navigation
    question_numbers = [q['qno'] for q in questions]
    
    stats = {
        'total_questions': total_questions,
        'questions_with_images': questions_with_images,
        'question_numbers': question_numbers
    }
    
    return render_template('index.html', questions=questions, stats=stats)

@app.route('/question/<qno>')
def view_question(qno):
    """View a specific question."""
    questions, error = load_questions()
    
    if error:
        return render_template('error.html', error=error)
    
    # Find the specific question
    question = None
    for q in questions:
        if str(q['qno']) == str(qno):
            question = q
            break
    
    if not question:
        return render_template('error.html', error=f"Question {qno} not found")
    
    return render_template('question.html', question=question)

@app.route('/images/<filename>')
def serve_image(filename):
    """Serve images from the images directory."""
    return send_from_directory(IMAGES_DIR, filename)

@app.route('/api/questions')
def api_questions():
    """API endpoint to get all questions as JSON."""
    questions, error = load_questions()
    
    if error:
        return jsonify({'error': error}), 500
    
    return jsonify({'questions': questions})

@app.route('/api/question/<qno>')
def api_question(qno):
    """API endpoint to get a specific question."""
    questions, error = load_questions()
    
    if error:
        return jsonify({'error': error}), 500
    
    # Find the specific question
    question = None
    for q in questions:
        if str(q['qno']) == str(qno):
            question = q
            break
    
    if not question:
        return jsonify({'error': f'Question {qno} not found'}), 404
    
    return jsonify({'question': question})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    print("🌐 Starting Question Viewer...")
    print("📁 Excel directory:", EXCEL_DIR)
    print("🖼️ Images directory:", IMAGES_DIR)
    
    # Check if files exist
    excel_file = get_latest_excel_file()
    if excel_file:
        print(f"📊 Found Excel file: {os.path.basename(excel_file)}")
    else:
        print("⚠️ No Excel file found!")
    
    print("\n🚀 Starting server at http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
