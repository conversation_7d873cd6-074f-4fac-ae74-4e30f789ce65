"""
Final Dataset Generator for Modern AI Pipeline
Creates clean pandas DataFrame and exports to Excel/CSV
"""

import logging
from pathlib import Path
from typing import List, Dict
import pandas as pd
import json

try:
    from .config import PipelineConfig
except ImportError:
    from config import PipelineConfig

class DatasetGenerator:
    """Generate final clean dataset from processed questions"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_dataset(self, questions: List[Dict]) -> pd.DataFrame:
        """
        Generate clean dataset from processed questions
        
        Args:
            questions: List of processed question dictionaries
            
        Returns:
            pandas DataFrame with clean structure
        """
        self.logger.info(f"Generating dataset from {len(questions)} questions")
        
        # Prepare data for DataFrame
        dataset_rows = []
        
        for question in questions:
            row = self._create_dataset_row(question)
            dataset_rows.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(dataset_rows)
        
        # Sort by question number
        df = df.sort_values('Question_Number').reset_index(drop=True)
        
        self.logger.info(f"Generated dataset with {len(df)} rows and {len(df.columns)} columns")
        return df
    
    def _create_dataset_row(self, question: Dict) -> Dict:
        """Create a single row for the dataset"""
        
        # Get image paths (ensure we have 4 option image paths)
        option_image_paths = question.get("option_image_paths", ["", "", "", ""])
        while len(option_image_paths) < 4:
            option_image_paths.append("")
        
        row = {
            "Question_Number": question.get("question_number", 0),
            "Question_Text": self._clean_text_for_export(question.get("question_text", "")),
            "Option_1": self._clean_text_for_export(question.get("option_1", "")),
            "Option_2": self._clean_text_for_export(question.get("option_2", "")),
            "Option_3": self._clean_text_for_export(question.get("option_3", "")),
            "Option_4": self._clean_text_for_export(question.get("option_4", "")),
            "Question_Image_Path": question.get("question_image_path", ""),
            "Option_1_Image_Path": option_image_paths[0],
            "Option_2_Image_Path": option_image_paths[1],
            "Option_3_Image_Path": option_image_paths[2],
            "Option_4_Image_Path": option_image_paths[3],
            "Table_JSON_Path": question.get("table_json_path", ""),
            "Page_Number": question.get("page_number", 0),
            "Has_Figure": question.get("has_figure", False),
            "Has_Table": question.get("has_table", False)
        }
        
        return row
    
    def _clean_text_for_export(self, text: str) -> str:
        """Clean text for CSV/Excel export"""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = " ".join(text.split())
        
        # Handle special characters for CSV
        text = text.replace('"', '""')  # Escape quotes
        
        # Convert common Unicode symbols
        unicode_replacements = {
            'μ': 'μ',
            'Δ': 'Δ',
            '→': '→',
            '°': '°',
            '²': '²',
            '³': '³',
            '×': '×',
            '÷': '÷',
            '±': '±',
            '≤': '≤',
            '≥': '≥',
            '≠': '≠',
            '∞': '∞',
            'π': 'π',
            'α': 'α',
            'β': 'β',
            'γ': 'γ',
            'θ': 'θ',
            'λ': 'λ',
            'ω': 'ω'
        }
        
        for unicode_char, replacement in unicode_replacements.items():
            text = text.replace(unicode_char, replacement)
        
        return text.strip()
    
    def save_dataset(self, df: pd.DataFrame, output_dir: Path = None) -> Dict[str, Path]:
        """
        Save dataset to multiple formats
        
        Args:
            df: DataFrame to save
            output_dir: Output directory (default: config.OUTPUT_DIR)
            
        Returns:
            Dictionary with paths to saved files
        """
        output_dir = output_dir or self.config.OUTPUT_DIR
        output_dir.mkdir(parents=True, exist_ok=True)
        
        saved_files = {}
        
        # Save as Excel
        excel_path = output_dir / "modern_questions.xlsx"
        try:
            df.to_excel(excel_path, index=False, engine='openpyxl')
            saved_files["excel"] = excel_path
            self.logger.info(f"Saved Excel file: {excel_path}")
        except Exception as e:
            self.logger.error(f"Failed to save Excel file: {e}")
        
        # Save as CSV
        csv_path = output_dir / "modern_questions.csv"
        try:
            df.to_csv(csv_path, index=False, encoding='utf-8')
            saved_files["csv"] = csv_path
            self.logger.info(f"Saved CSV file: {csv_path}")
        except Exception as e:
            self.logger.error(f"Failed to save CSV file: {e}")
        
        # Save as JSON (for backup)
        json_path = output_dir / "modern_questions.json"
        try:
            df.to_json(json_path, orient='records', indent=2, force_ascii=False)
            saved_files["json"] = json_path
            self.logger.info(f"Saved JSON file: {json_path}")
        except Exception as e:
            self.logger.error(f"Failed to save JSON file: {e}")
        
        return saved_files
    
    def generate_summary_report(self, df: pd.DataFrame, output_dir: Path = None) -> Path:
        """Generate a summary report of the extraction"""
        output_dir = output_dir or self.config.OUTPUT_DIR
        report_path = output_dir / "extraction_summary.md"
        
        # Calculate statistics
        total_questions = len(df)
        questions_with_images = df['Has_Figure'].sum()
        questions_with_tables = df['Has_Table'].sum()
        pages_processed = df['Page_Number'].nunique()
        
        # Count non-empty fields
        non_empty_question_text = df['Question_Text'].str.len().gt(0).sum()
        non_empty_options = sum([
            df[f'Option_{i}'].str.len().gt(0).sum() 
            for i in range(1, 5)
        ]) / 4  # Average across 4 options
        
        # Generate report
        report_content = f"""# Modern AI Pipeline Extraction Summary
        
## 📊 Extraction Statistics

- **Total Questions Extracted**: {total_questions}
- **Pages Processed**: {pages_processed}
- **Questions with Images**: {questions_with_images} ({questions_with_images/total_questions*100:.1f}%)
- **Questions with Tables**: {questions_with_tables} ({questions_with_tables/total_questions*100:.1f}%)

## ✅ Data Quality

- **Questions with Text**: {non_empty_question_text}/{total_questions} ({non_empty_question_text/total_questions*100:.1f}%)
- **Average Options per Question**: {non_empty_options:.1f}/4

## 📁 Output Files

- **Excel**: modern_questions.xlsx
- **CSV**: modern_questions.csv  
- **JSON**: modern_questions.json
- **Images**: images/ directory
- **Tables**: tables/ directory

## 🎯 Question Number Range

- **First Question**: {df['Question_Number'].min()}
- **Last Question**: {df['Question_Number'].max()}

## 📋 Column Structure

{self._get_column_info(df)}

## 🚀 Modern AI Pipeline Features Used

✅ **PDF to High-Quality Images** (300 DPI)
✅ **EasyOCR Text Recognition** 
✅ **Computer Vision Layout Analysis**
✅ **Spatial Block Grouping**
✅ **Logo Filtering** (Aakash logos removed)
✅ **Solution Detection & Skipping**
✅ **Structured Data Extraction**
✅ **Image Cropping & Table Processing**

---
*Generated by Modern AI Document Understanding Pipeline*
"""
        
        # Save report
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        self.logger.info(f"Generated summary report: {report_path}")
        return report_path
    
    def _get_column_info(self, df: pd.DataFrame) -> str:
        """Get information about DataFrame columns"""
        column_info = []
        
        for col in df.columns:
            non_null_count = df[col].notna().sum()
            non_empty_count = df[col].astype(str).str.len().gt(0).sum()
            
            column_info.append(f"- **{col}**: {non_empty_count}/{len(df)} non-empty")
        
        return "\n".join(column_info)


def test_generator():
    """Test the dataset generator"""
    config = PipelineConfig()
    generator = DatasetGenerator(config)
    
    # Mock processed questions
    mock_questions = [
        {
            "question_number": 1,
            "question_text": "What is the speed of light in vacuum?",
            "option_1": "3 × 10^8 m/s",
            "option_2": "3 × 10^6 m/s", 
            "option_3": "3 × 10^10 m/s",
            "option_4": "3 × 10^12 m/s",
            "page_number": 1,
            "has_figure": False,
            "has_table": False,
            "question_image_path": "",
            "option_image_paths": ["", "", "", ""],
            "table_json_path": ""
        },
        {
            "question_number": 2,
            "question_text": "Which of the following is a vector quantity?",
            "option_1": "Speed",
            "option_2": "Velocity",
            "option_3": "Distance", 
            "option_4": "Time",
            "page_number": 1,
            "has_figure": True,
            "has_table": False,
            "question_image_path": "images/Q2_img_0.png",
            "option_image_paths": ["", "", "", ""],
            "table_json_path": ""
        }
    ]
    
    # Generate dataset
    df = generator.generate_dataset(mock_questions)
    print(f"Generated DataFrame:\n{df}")
    
    # Save dataset
    saved_files = generator.save_dataset(df)
    print(f"Saved files: {saved_files}")
    
    # Generate report
    report_path = generator.generate_summary_report(df)
    print(f"Generated report: {report_path}")


if __name__ == "__main__":
    test_generator()
