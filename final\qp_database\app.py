from src.extractor import StartProcessing
import logging
import os
import google.generativeai as genai
import json
from dotenv import load_dotenv


load_dotenv()

if __name__ == "__main__":

    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s',filename="pdf_processor.log",filemode="a")

    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
    if not GEMINI_API_KEY:
        raise ValueError("No GEMINI_API_KEY found in .env file")
    genai.configure(api_key=GEMINI_API_KEY)


    # Load JSON config
    with open("src/config/config.json", "r") as f:
        config = json.load(f)

    # Access values
    input_dir = config["input_dir"]
    output_excel_dir = config["output_excel_dir"]
    output_image_dir = config["output_image_dir"]

    obj = StartProcessing(input_dir,output_excel_dir,output_image_dir)
    obj.extract_all_questions_from_pdf()
