# Modern AI Document Understanding Pipeline Configuration
# Open Source Implementation for NEET/JEE Question Extraction

import os
from pathlib import Path

class PipelineConfig:
    """Configuration for the modern AI document pipeline"""
    
    # Processing limits (as requested)
    MAX_PAGES = 20  # Process only first 20 pages
    MAX_QUESTIONS = 48  # Expected ~48 questions from 20 pages
    
    # Image processing settings
    PDF_DPI = 300  # High quality for AI models
    IMAGE_FORMAT = "PNG"
    
    # Model settings
    LAYOUT_MODEL = "microsoft/layoutlmv3-base"  # For document layout analysis
    OCR_MODEL = "microsoft/trocr-base-printed"  # For text recognition
    
    # Output directories
    BASE_DIR = Path(__file__).parent.parent
    OUTPUT_DIR = BASE_DIR / "modern_output"
    IMAGES_DIR = OUTPUT_DIR / "images"
    TABLES_DIR = OUTPUT_DIR / "tables"
    TEMP_DIR = OUTPUT_DIR / "temp"
    
    # File paths
    INPUT_PDF = BASE_DIR / "akash.pdf"
    
    # Solution detection patterns (to skip)
    SOLUTION_PATTERNS = [
        r"Sol\.",
        r"Solution",
        r"Answer\s*\([1-4]\)",
        r"Correct\s*Answer",
        r"Explanation"
    ]
    
    # Logo filtering (Aakash specific)
    LOGO_SIZES = [
        (403, 284),  # Main Aakash logo size
        (200, 150),  # Smaller logo variants
    ]
    
    # Question detection patterns
    QUESTION_PATTERNS = [
        r"^\d+\.",  # 1., 2., 3., etc.
        r"^Q\d+",   # Q1, Q2, etc.
    ]
    
    # Option patterns
    OPTION_PATTERNS = [
        r"\(1\)",
        r"\(2\)",
        r"\(3\)",
        r"\(4\)"
    ]
    
    @classmethod
    def setup_directories(cls):
        """Create necessary directories"""
        for dir_path in [cls.OUTPUT_DIR, cls.IMAGES_DIR, cls.TABLES_DIR, cls.TEMP_DIR]:
            dir_path.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_temp_image_path(cls, page_num):
        """Get path for temporary page image"""
        return cls.TEMP_DIR / f"page_{page_num:03d}.png"
    
    @classmethod
    def get_question_image_path(cls, question_num, img_index=0):
        """Get path for question image"""
        return cls.IMAGES_DIR / f"Q{question_num}_img_{img_index}.png"
    
    @classmethod
    def get_table_json_path(cls, question_num):
        """Get path for table JSON file"""
        return cls.TABLES_DIR / f"Q{question_num}_table.json"
