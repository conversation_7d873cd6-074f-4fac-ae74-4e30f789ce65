2025-07-09 18:30:04,825 - INFO - Excel file saved with integrated LaTeX format: C:\varala\qp_database\data\output\excel\clgdunia_questions.xlsx
2025-07-09 19:00:46,823 - ERROR - Error reading PDF clgdunia.pdf: 'StartProcessing' object has no attribute 'detect_bounding_boxes'
2025-07-09 19:02:02,853 - ERROR - Gemini extraction failed: 'StartProcessing' object has no attribute 'model'
2025-07-09 19:02:02,853 - ERROR - Error reading PDF clgdunia.pdf: 'StartProcessing' object has no attribute 'map_images_to_questions_unified'
2025-07-09 19:03:05,653 - ERROR - Gemini extraction failed: 'StartProcessing' object has no attribute 'model'
2025-07-09 19:03:05,655 - INFO -   _ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:03:05,655 - INFO -   Q_ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:03:05,655 - INFO -    -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:03:05,655 - INFO -   Q -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:03:05,656 - ERROR - Error reading PDF clgdunia.pdf: 'StartProcessing' object has no attribute 'save_to_excel_with_structured_format'
2025-07-09 19:04:15,488 - ERROR - Gemini extraction failed: 'StartProcessing' object has no attribute 'model'
2025-07-09 19:04:15,489 - INFO -   _ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:04:15,489 - INFO -   Q_ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:04:15,489 - INFO -    -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:04:15,489 - INFO -   Q -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:04:16,124 - ERROR - Error saving to Excel with integrated LaTeX format: "None of [Index(['qno', 'language', 'question', 'optiona', 'optionb', 'optionc',\n       'optiond', 'type_of_question', 'mark', 'blooms_level'],\n      dtype='object')] are in the [columns]"
2025-07-09 19:05:46,484 - ERROR - Gemini extraction failed: 'StartProcessing' object has no attribute 'model'
2025-07-09 19:05:46,484 - INFO -   _ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:05:46,484 - INFO -   Q_ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:05:46,485 - INFO -    -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:05:46,485 - INFO -   Q -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:05:46,860 - INFO - Excel file saved with integrated LaTeX format: C:\varala\qp_database\data\output\excel\clgdunia_questions.xlsx
2025-07-09 19:24:37,035 - ERROR - PDF file does not exist: C:\varala\qp_database\clgdunia.pdf
2025-07-09 19:25:03,823 - ERROR - PDF file does not exist: C:\varala\qp_database\clgdunia.pdf
2025-07-09 19:27:26,901 - ERROR - Gemini extraction failed: 'StartProcessing' object has no attribute 'model'
2025-07-09 19:27:26,902 - INFO -   _ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:27:26,902 - INFO -   Q_ -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:27:26,902 - INFO -    -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:27:26,903 - INFO -   Q -> Main: 0, Options: {opt: len(imgs) for opt, imgs in v['options'].items() if imgs}
2025-07-09 19:27:27,452 - INFO - Excel file saved with integrated LaTeX format: C:\varala\qp_database\data\output\excel\clgdunia_questions.xlsx
