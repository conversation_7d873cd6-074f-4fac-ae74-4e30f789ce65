import os
import time
from src.base import PDFProcessor
import fitz


class StartProcessing (PDFProcessor):

    def __init__(self,input_dir,excel_output_dir,images_dir):
        self.input_dir=input_dir
        self.excel_output_dir=excel_output_dir
        self.images_dir = images_dir

    

    #extract content from question paper with enhanced processing
    def extract_all_questions_from_pdf(self, max_questions=30):
        start_time = time.time()

        # Initialize the PDFProcessor properly
        super().__init__(
            self.input_dir,
            self.excel_output_dir,
            self.images_dir
        )

        # List all PDF files in input_dir
        pdf_files = [os.path.join(self.input_dir, f) for f in os.listdir(self.input_dir) if f.lower().endswith(".pdf")]

        if not pdf_files:
            print("No PDF files found in the input directory.")
            return

        for pdf_path in pdf_files:
            print(f"🔄 Processing {os.path.basename(pdf_path)}...")
            print(f"📄 Extracting first {max_questions} questions with enhanced image mapping...")

            # Get total number of pages in the PDF
            try:
                doc = fitz.open(pdf_path)
                total_pages = len(doc)
                doc.close()

                print(f"📖 PDF has {total_pages} pages")

                # Process pages 2-10 for first 30 questions (skip page 1 which is usually instructions)
                start_page = 2
                end_page = min(10, total_pages)  # Process up to page 10 or total pages, whichever is smaller

                print(f"🔍 Processing pages {start_page} to {end_page}")
                success = self.process_pdf(pdf_path, start_page=start_page, end_page=end_page, max_questions=max_questions)

            except Exception as e:
                print(f"❌ Error reading PDF {pdf_path}: {e}")
                success = False

            if success:
                print(f"✅ Successfully processed: {os.path.basename(pdf_path)}")
            else:
                print(f"❌ Failed to process: {os.path.basename(pdf_path)}")

        end_time = time.time()
        execution_time = end_time - start_time
        print(f"⏱️  Total execution time: {execution_time:.2f} seconds")


