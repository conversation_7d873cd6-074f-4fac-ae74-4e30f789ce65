import os
import time
from qp.qp_database.base import PDFProcessor
import fitz


class StartProcessing (PDFProcessor):

    def __init__(self,input_dir,excel_output_dir,images_dir):
        self.input_dir=input_dir
        self.excel_output_dir=excel_output_dir
        self.images_dir = images_dir

    

    #extract content from question paper
    def extract_all_questions_from_pdf(self):
        start_time = time.time()

        processor = PDFProcessor.__init__(self,
        self.input_dir,
        self.excel_output_dir,
        self.images_dir
        )   

        start_time = time.time()

        # List all PDF files in input_dir
        pdf_files = [os.path.join(self.input_dir, f) for f in os.listdir(self.input_dir) if f.lower().endswith(".pdf")]

        if not pdf_files:
            print("No PDF files found in the input directory.")
            return

        for pdf_path in pdf_files:
            print(f"Processing {pdf_path}...")
            
            # Get total number of pages in the PDF
            try:
                doc = fitz.open(pdf_path)
                total_pages = len(doc)
                doc.close()
                
                # Process only first 10 pages (from 1 to 10)
                end_page = min(10, total_pages)  # Limit to first 10 pages or total pages if less than 10
                print(f"📄 PDF has {total_pages} pages, processing pages 1 to {end_page} (first 10 pages only)")
                success = self.process_pdf(pdf_path, start_page=1, end_page=end_page)
            except Exception as e:
                print(f"Error reading PDF {pdf_path}: {e}")
                success = False

            if success:
                print(f"✅ Successfully processed: {os.path.basename(pdf_path)}")
            else:
                print(f"❌ Failed to process: {os.path.basename(pdf_path)}")
        
        end_time = time.time()
        execution_time = end_time - start_time
        print(f"Execution time for question extraction: {execution_time:.2f} seconds")


