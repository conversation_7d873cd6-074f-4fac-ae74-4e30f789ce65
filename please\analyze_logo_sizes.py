import fitz  # PyMuPDF
from PIL import Image
import io
from collections import Counter

def analyze_logo_sizes():
    """Analyze exact sizes of images to identify Aakash logo patterns"""
    
    pdf_path = "akash.pdf"
    doc = fitz.open(pdf_path)
    
    image_sizes = []
    logo_candidates = []
    
    print("🔍 Analyzing image sizes in PDF to identify Aakash logos...")
    
    # Check first 10 pages to identify patterns
    for page_num in range(min(10, len(doc))):
        page = doc[page_num]
        image_list = page.get_images()
        
        print(f"\nPage {page_num + 1}: Found {len(image_list)} images")
        
        for img_index, img in enumerate(image_list):
            try:
                # Get image data
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                    pil_img = Image.open(io.BytesIO(img_data))
                    
                    width, height = pil_img.size
                    size_key = f"{width}x{height}"
                    image_sizes.append(size_key)
                    
                    # Potential logo candidates (small to medium sized, square-ish)
                    aspect_ratio = width / height
                    if (width <= 500 and height <= 400 and 
                        0.5 <= aspect_ratio <= 2.0):
                        logo_candidates.append({
                            'page': page_num + 1,
                            'size': size_key,
                            'width': width,
                            'height': height,
                            'aspect_ratio': round(aspect_ratio, 2)
                        })
                        print(f"  Logo candidate: {size_key} (ratio: {aspect_ratio:.2f})")
                    else:
                        print(f"  Content image: {size_key} (ratio: {aspect_ratio:.2f})")
                
                pix = None
                
            except Exception as e:
                print(f"  Error with image {img_index}: {e}")
                continue
    
    doc.close()
    
    # Analyze patterns
    print(f"\n📊 IMAGE SIZE ANALYSIS:")
    size_counts = Counter(image_sizes)
    
    print(f"\nMost common image sizes:")
    for size, count in size_counts.most_common(10):
        print(f"  {size}: {count} occurrences")
    
    print(f"\n🎯 LOGO CANDIDATES (small/medium, square-ish):")
    logo_size_counts = Counter([candidate['size'] for candidate in logo_candidates])
    
    for size, count in logo_size_counts.most_common():
        print(f"  {size}: {count} occurrences - LIKELY LOGO")
    
    # Identify exact Aakash logo size
    print(f"\n🔍 RECOMMENDED LOGO FILTERS:")
    for size, count in logo_size_counts.most_common(3):
        width, height = map(int, size.split('x'))
        print(f"  Filter: {width}x{height} (found {count} times)")
        print(f"    if width == {width} and height == {height}: return True")
    
    return logo_size_counts.most_common(3)

if __name__ == "__main__":
    analyze_logo_sizes()
