"""
Modern AI Document Understanding Pipeline - Main Orchestrator
Coordinates all components for end-to-end question extraction
"""

import logging
import time
from pathlib import Path
from typing import List, Dict, Optional
import sys

# Add current directory to path for imports
sys.path.append(str(Path(__file__).parent))

try:
    from .config import PipelineConfig
    from .pdf_converter import PDFToImageConverter
    from .document_analyzer import DocumentLayoutAnalyzer
    from .data_extractor import StructuredDataExtractor
    from .image_table_processor import ImageTableProcessor
    from .dataset_generator import DatasetGenerator
except ImportError:
    # Fallback for direct execution
    from config import PipelineConfig
    from pdf_converter import PDFToImageConverter
    from document_analyzer import DocumentLayoutAnalyzer
    from data_extractor import StructuredDataExtractor
    from image_table_processor import ImageTableProcessor
    from dataset_generator import DatasetGenerator

class ModernAIPipeline:
    """Main pipeline orchestrator for modern AI document understanding"""
    
    def __init__(self, pdf_path: str, config: PipelineConfig = None):
        self.pdf_path = Path(pdf_path)
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
        
        # Initialize components
        self.pdf_converter = PDFToImageConverter(str(self.pdf_path), self.config)
        self.document_analyzer = DocumentLayoutAnalyzer(self.config)
        self.data_extractor = StructuredDataExtractor(self.config)
        self.image_processor = ImageTableProcessor(self.config)
        self.dataset_generator = DatasetGenerator(self.config)
        
        self.logger.info(f"Initialized Modern AI Pipeline for: {self.pdf_path.name}")
    
    def _setup_logger(self):
        """Setup logging for the pipeline"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # Console handler
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # File handler
            log_file = self.config.OUTPUT_DIR / "pipeline.log"
            self.config.setup_directories()
            file_handler = logging.FileHandler(log_file)
            file_handler.setFormatter(console_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def run_complete_pipeline(self, max_pages: int = None) -> Dict:
        """
        Run the complete modern AI pipeline
        
        Args:
            max_pages: Maximum pages to process (default: config.MAX_PAGES)
            
        Returns:
            Dictionary with pipeline results and statistics
        """
        start_time = time.time()
        max_pages = max_pages or self.config.MAX_PAGES
        
        self.logger.info("🚀 Starting Modern AI Document Understanding Pipeline")
        self.logger.info(f"📄 Processing: {self.pdf_path.name}")
        self.logger.info(f"📊 Max pages: {max_pages}")
        
        try:
            # Step 1: Convert PDF to high-quality images
            self.logger.info("📸 Step 1: Converting PDF to high-quality images...")
            image_paths = self.pdf_converter.convert_pages_to_images(max_pages)
            self.logger.info(f"✅ Converted {len(image_paths)} pages to images")
            
            # Step 2: Analyze document layout for each page
            self.logger.info("🤖 Step 2: Analyzing document layout with AI...")
            page_analyses = []
            for i, image_path in enumerate(image_paths):
                self.logger.info(f"   Analyzing page {i+1}/{len(image_paths)}")
                analysis = self.document_analyzer.analyze_page(image_path)
                page_analyses.append(analysis)
            self.logger.info(f"✅ Analyzed {len(page_analyses)} pages")
            
            # Step 3: Extract structured question data
            self.logger.info("📋 Step 3: Extracting structured question data...")
            questions = self.data_extractor.extract_questions_from_pages(page_analyses)
            self.logger.info(f"✅ Extracted {len(questions)} questions")
            
            # Step 4: Process images and tables
            self.logger.info("🖼️ Step 4: Processing images and tables...")
            processed_questions = self.image_processor.process_questions(questions, page_analyses)
            self.logger.info("✅ Completed image and table processing")
            
            # Step 5: Generate final dataset
            self.logger.info("📊 Step 5: Generating final dataset...")
            dataset_df = self.dataset_generator.generate_dataset(processed_questions)
            saved_files = self.dataset_generator.save_dataset(dataset_df)
            report_path = self.dataset_generator.generate_summary_report(dataset_df)
            self.logger.info("✅ Generated final dataset and report")
            
            # Cleanup temporary files
            self.logger.info("🧹 Cleaning up temporary files...")
            self.pdf_converter.cleanup_temp_images()
            self.image_processor.cleanup_temp_files()
            
            # Calculate statistics
            end_time = time.time()
            processing_time = end_time - start_time
            
            results = {
                "success": True,
                "total_questions": len(processed_questions),
                "pages_processed": len(page_analyses),
                "questions_with_images": sum(1 for q in processed_questions if q.get("has_figure", False)),
                "questions_with_tables": sum(1 for q in processed_questions if q.get("has_table", False)),
                "processing_time_seconds": processing_time,
                "output_files": saved_files,
                "report_path": report_path,
                "dataset_shape": dataset_df.shape
            }
            
            self.logger.info("🎉 Pipeline completed successfully!")
            self.logger.info(f"📊 Results: {results['total_questions']} questions from {results['pages_processed']} pages")
            self.logger.info(f"⏱️ Processing time: {processing_time:.2f} seconds")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline failed: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "processing_time_seconds": time.time() - start_time
            }
    
    def run_analysis_only(self, max_pages: int = None) -> List[Dict]:
        """Run only the analysis part (for debugging)"""
        max_pages = max_pages or self.config.MAX_PAGES
        
        self.logger.info("🔍 Running analysis-only mode...")
        
        # Convert PDF to images
        image_paths = self.pdf_converter.convert_pages_to_images(max_pages)
        
        # Analyze pages
        page_analyses = []
        for image_path in image_paths:
            analysis = self.document_analyzer.analyze_page(image_path)
            page_analyses.append(analysis)
        
        return page_analyses
    
    def get_pdf_info(self) -> Dict:
        """Get information about the input PDF"""
        return self.pdf_converter.get_page_info()


def main():
    """Main function to run the pipeline"""
    # Setup
    config = PipelineConfig()
    
    # Check if PDF exists
    if not config.INPUT_PDF.exists():
        print(f"❌ PDF not found: {config.INPUT_PDF}")
        print("Please ensure akash.pdf is in the correct location.")
        return
    
    # Initialize pipeline
    pipeline = ModernAIPipeline(str(config.INPUT_PDF), config)
    
    # Get PDF info
    pdf_info = pipeline.get_pdf_info()
    print(f"📄 PDF Info: {pdf_info}")
    
    # Run pipeline
    print("\n🚀 Starting Modern AI Pipeline...")
    results = pipeline.run_complete_pipeline()
    
    if results["success"]:
        print("\n✅ Pipeline completed successfully!")
        print(f"📊 Extracted {results['total_questions']} questions")
        print(f"📁 Output files:")
        for file_type, file_path in results["output_files"].items():
            print(f"   - {file_type.upper()}: {file_path}")
        print(f"📋 Report: {results['report_path']}")
    else:
        print(f"\n❌ Pipeline failed: {results['error']}")


if __name__ == "__main__":
    main()
