"""
Quick Test of Modern AI Pipeline - Process 3 pages only
Demonstrates the modern approach vs traditional method
"""

import sys
from pathlib import Path
import time

# Add the modern pipeline to path
sys.path.append(str(Path(__file__).parent / "modern_ai_pipeline"))

from modern_ai_pipeline.main_pipeline import ModernAIPipeline
from modern_ai_pipeline.config import PipelineConfig

def test_modern_pipeline():
    """Test the modern AI pipeline with just 3 pages"""
    print("🚀 Testing Modern AI Document Understanding Pipeline")
    print("=" * 60)
    
    # Setup config for quick test
    config = PipelineConfig()
    config.MAX_PAGES = 3  # Process only 3 pages for quick test
    
    # Check if PDF exists
    if not config.INPUT_PDF.exists():
        print(f"❌ PDF not found: {config.INPUT_PDF}")
        return
    
    # Initialize pipeline
    pipeline = ModernAIPipeline(str(config.INPUT_PDF), config)
    
    # Get PDF info
    pdf_info = pipeline.get_pdf_info()
    print(f"📄 PDF Info:")
    print(f"   - Total pages: {pdf_info['total_pages']}")
    print(f"   - File size: {pdf_info['file_size_mb']:.1f} MB")
    print(f"   - Author: {pdf_info['author']}")
    
    print(f"\n🎯 Processing first {config.MAX_PAGES} pages only (for quick demo)")
    
    # Run pipeline
    start_time = time.time()
    results = pipeline.run_complete_pipeline()
    end_time = time.time()
    
    print("\n" + "=" * 60)
    
    if results["success"]:
        print("✅ Modern AI Pipeline completed successfully!")
        print(f"\n📊 Results Summary:")
        print(f"   - Questions extracted: {results['total_questions']}")
        print(f"   - Pages processed: {results['pages_processed']}")
        print(f"   - Questions with images: {results['questions_with_images']}")
        print(f"   - Questions with tables: {results['questions_with_tables']}")
        print(f"   - Processing time: {end_time - start_time:.1f} seconds")
        
        print(f"\n📁 Output files created:")
        for file_type, file_path in results["output_files"].items():
            if Path(file_path).exists():
                size_kb = Path(file_path).stat().st_size / 1024
                print(f"   - {file_type.upper()}: {file_path} ({size_kb:.1f} KB)")
        
        print(f"\n📋 Summary report: {results['report_path']}")
        
        # Show comparison with traditional method
        print(f"\n🔄 Comparison with Traditional Method:")
        print(f"   Modern AI Pipeline:")
        print(f"   ✅ End-to-end document understanding")
        print(f"   ✅ Spatial layout analysis") 
        print(f"   ✅ Automatic solution detection & skipping")
        print(f"   ✅ Logo filtering without manual tuning")
        print(f"   ✅ Structured data extraction")
        
        return True
        
    else:
        print(f"❌ Pipeline failed: {results['error']}")
        return False

def show_sample_output():
    """Show sample extracted data"""
    config = PipelineConfig()
    
    # Check if output files exist
    excel_file = config.OUTPUT_DIR / "modern_questions.xlsx"
    csv_file = config.OUTPUT_DIR / "modern_questions.csv"
    
    if csv_file.exists():
        print(f"\n📋 Sample extracted data:")
        print("-" * 40)
        
        # Read first few lines of CSV
        with open(csv_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()[:4]  # Header + 3 data rows
            for i, line in enumerate(lines):
                if i == 0:
                    print("HEADERS:", line.strip())
                else:
                    # Show just question number and text (truncated)
                    parts = line.split(',')
                    if len(parts) >= 2:
                        q_num = parts[0]
                        q_text = parts[1][:50] + "..." if len(parts[1]) > 50 else parts[1]
                        print(f"Q{q_num}: {q_text}")

if __name__ == "__main__":
    print("🧪 Modern AI Pipeline Quick Test")
    print("Processing 3 pages to demonstrate the approach...\n")
    
    success = test_modern_pipeline()
    
    if success:
        show_sample_output()
        print(f"\n🎉 Modern AI Pipeline demonstration complete!")
        print(f"💡 This approach scales to full documents with better accuracy")
    else:
        print(f"\n❌ Test failed - check the error messages above")
