"""
Image Cropping and Table Extraction Module
Processes detected figures and tables from document analysis
"""

import logging
import json
from pathlib import Path
from typing import List, Dict, Optional, Tuple
import cv2
import numpy as np
from PIL import Image

try:
    from .config import PipelineConfig
except ImportError:
    from config import PipelineConfig

class ImageTableProcessor:
    """Process images and tables from document analysis results"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def process_questions(self, questions: List[Dict], page_analyses: List[Dict]) -> List[Dict]:
        """
        Process images and tables for all questions
        
        Args:
            questions: List of structured questions
            page_analyses: List of page analysis results
            
        Returns:
            Updated questions with image and table paths
        """
        self.logger.info(f"Processing images and tables for {len(questions)} questions")
        
        # Create page lookup
        page_lookup = {i + 1: page_data for i, page_data in enumerate(page_analyses)}
        
        processed_questions = []
        
        for question in questions:
            processed_question = question.copy()
            
            # Process images for this question
            if question.get("has_figure", False):
                image_paths = self._extract_question_images(question, page_lookup)
                processed_question["question_image_path"] = image_paths[0] if image_paths else ""
                processed_question["option_image_paths"] = image_paths[1:5] if len(image_paths) > 1 else ["", "", "", ""]
            else:
                processed_question["question_image_path"] = ""
                processed_question["option_image_paths"] = ["", "", "", ""]
            
            # Process tables for this question
            if question.get("has_table", False):
                table_path = self._extract_question_table(question, page_lookup)
                processed_question["table_json_path"] = table_path
            else:
                processed_question["table_json_path"] = ""
            
            processed_questions.append(processed_question)
        
        self.logger.info("Completed processing images and tables")
        return processed_questions
    
    def _extract_question_images(self, question: Dict, page_lookup: Dict) -> List[str]:
        """Extract and crop images for a question"""
        page_num = question.get("page_number", 1)
        page_data = page_lookup.get(page_num)
        
        if not page_data:
            return []
        
        page_image_path = page_data.get("page_path")
        if not page_image_path or not Path(page_image_path).exists():
            return []
        
        # Load the page image
        image = cv2.imread(page_image_path)
        if image is None:
            return []
        
        image_paths = []
        figure_bboxes = question.get("figure_bboxes", [])
        
        for i, bbox in enumerate(figure_bboxes):
            try:
                # Crop the image
                cropped_image = self._crop_image_with_padding(image, bbox)
                
                # Save the cropped image
                output_path = self.config.get_question_image_path(
                    question["question_number"], i
                )
                
                # Ensure directory exists
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Save image
                cv2.imwrite(str(output_path), cropped_image)
                image_paths.append(str(output_path))
                
                self.logger.debug(f"Saved image: {output_path}")
                
            except Exception as e:
                self.logger.warning(f"Failed to crop image for Q{question['question_number']}: {e}")
        
        return image_paths
    
    def _crop_image_with_padding(self, image: np.ndarray, bbox: List[int], 
                                padding: int = 10) -> np.ndarray:
        """Crop image with padding around bounding box"""
        x1, y1, x2, y2 = bbox
        h, w = image.shape[:2]
        
        # Add padding
        x1 = max(0, x1 - padding)
        y1 = max(0, y1 - padding)
        x2 = min(w, x2 + padding)
        y2 = min(h, y2 + padding)
        
        # Crop the image
        cropped = image[y1:y2, x1:x2]
        
        return cropped
    
    def _extract_question_table(self, question: Dict, page_lookup: Dict) -> str:
        """Extract and process table data for a question"""
        page_num = question.get("page_number", 1)
        page_data = page_lookup.get(page_num)
        
        if not page_data:
            return ""
        
        table_bboxes = question.get("table_bboxes", [])
        if not table_bboxes:
            return ""
        
        try:
            # Extract table data (simplified approach)
            table_data = self._extract_table_data(page_data, table_bboxes[0])
            
            # Save table JSON
            output_path = self.config.get_table_json_path(question["question_number"])
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(table_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Saved table: {output_path}")
            return str(output_path)
            
        except Exception as e:
            self.logger.warning(f"Failed to extract table for Q{question['question_number']}: {e}")
            return ""
    
    def _extract_table_data(self, page_data: Dict, table_bbox: List[int]) -> Dict:
        """Extract structured table data from a region"""
        # Get text blocks in the table region
        text_blocks = page_data.get("text_blocks", [])
        table_texts = []
        
        tx1, ty1, tx2, ty2 = table_bbox
        
        for block in text_blocks:
            bx1, by1, bx2, by2 = block["bbox"]
            
            # Check if block is within table region
            if not (bx2 < tx1 or bx1 > tx2 or by2 < ty1 or by1 > ty2):
                table_texts.append({
                    "text": block["text"],
                    "x": (bx1 + bx2) / 2,
                    "y": (by1 + by2) / 2
                })
        
        # Sort by position (top to bottom, left to right)
        table_texts.sort(key=lambda x: (x["y"], x["x"]))
        
        # Try to detect table structure
        table_data = self._parse_table_structure(table_texts)
        
        return table_data
    
    def _parse_table_structure(self, table_texts: List[Dict]) -> Dict:
        """Parse table structure from text blocks"""
        # Simple table parsing - can be enhanced
        rows = []
        current_row = []
        current_y = None
        y_threshold = 20  # Pixels tolerance for same row
        
        for text_item in table_texts:
            text = text_item["text"].strip()
            y = text_item["y"]
            
            if current_y is None:
                current_y = y
                current_row = [text]
            elif abs(y - current_y) <= y_threshold:
                # Same row
                current_row.append(text)
            else:
                # New row
                if current_row:
                    rows.append(current_row)
                current_row = [text]
                current_y = y
        
        # Add last row
        if current_row:
            rows.append(current_row)
        
        # Detect if it's a match-the-following table
        is_match_table = any("list" in " ".join(row).lower() for row in rows[:2])
        
        table_structure = {
            "type": "match_the_following" if is_match_table else "general_table",
            "rows": rows,
            "num_rows": len(rows),
            "num_cols": max(len(row) for row in rows) if rows else 0
        }
        
        # For match-the-following, try to separate List-I and List-II
        if is_match_table and len(rows) > 2:
            table_structure["list_i"] = []
            table_structure["list_ii"] = []
            
            for row in rows[1:]:  # Skip header
                if len(row) >= 2:
                    table_structure["list_i"].append(row[0])
                    table_structure["list_ii"].append(row[1])
        
        return table_structure
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        temp_dir = self.config.TEMP_DIR
        if temp_dir.exists():
            for file in temp_dir.glob("*"):
                if file.is_file():
                    file.unlink()
            self.logger.info("Cleaned up temporary files")


def test_processor():
    """Test the image and table processor"""
    config = PipelineConfig()
    processor = ImageTableProcessor(config)
    
    # Mock question data
    mock_questions = [{
        "question_number": 1,
        "question_text": "Test question",
        "page_number": 1,
        "has_figure": True,
        "has_table": False,
        "figure_bboxes": [[100, 100, 300, 200]],
        "table_bboxes": []
    }]
    
    # Mock page data
    mock_page_data = [{
        "page_path": str(config.TEMP_DIR / "page_001.png"),
        "text_blocks": []
    }]
    
    # Test processing (will fail without actual images)
    try:
        processed = processor.process_questions(mock_questions, mock_page_data)
        print(f"Processed questions: {json.dumps(processed, indent=2)}")
    except Exception as e:
        print(f"Test failed (expected without real images): {e}")


if __name__ == "__main__":
    test_processor()
