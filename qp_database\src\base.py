import os
import re
import json
import glob
import time
import shutil
import logging
import pandas as pd
import google.generativeai as genai
from dotenv import load_dotenv
import fitz  # PyMuPDF
import cv2
import tempfile
from datetime import datetime
from pathlib import Path
# filepath: c:\varala\qp_database\src\base.py

class PDFProcessor:
    def __init__(self, input_dir, excel_output_dir, images_dir):
        self.input_dir = input_dir
        self.excel_output_dir = excel_output_dir
        self.image_dir = images_dir

    def extract_content_images_yolo(self, pdf_path, start_page, end_page):
        """Extract all content images using YOLO, ignoring logos at page footers and bottom-right."""
        doc = fitz.open(pdf_path)
        content_images = []
        for page_num in range(start_page, end_page + 1):
            page = doc[page_num - 1]
            # Render page as image
            pix = page.get_pixmap(dpi=300)
            img_path = os.path.join(tempfile.gettempdir(), f"page_{page_num}.jpg")
            pix.save(img_path)
            # Use YOLO to detect bounding boxes
            boxes = self.detect_bounding_boxes(img_path)
            for idx, (x1, y1, x2, y2) in enumerate(boxes):
                width, height = x2 - x1, y2 - y1
                page_width, page_height = pix.width, pix.height
                # Ignore logos: bottom right, small, wide, or square, or typical logo aspect ratio
                is_bottom = y1 > page_height * 0.85
                is_right = x1 > page_width * 0.7
                is_small = width < 50 or height < 50
                aspect_ratio = width / height if height > 0 else 0
                is_logo_aspect = 0.8 < aspect_ratio < 1.2  # nearly square
                is_very_wide = aspect_ratio > 3
                if (is_bottom and is_right) or is_small or is_logo_aspect or is_very_wide:
                    continue
                # Save cropped image
                img = cv2.imread(img_path)
                crop = img[y1:y2, x1:x2]
                crop_path = os.path.join(self.image_dir, f"content_img_page{page_num}_{idx}.jpg")
                cv2.imwrite(crop_path, crop)
                content_images.append({
                    'path': crop_path,
                    'filename': os.path.basename(crop_path),
                    'page': page_num,
                    'position': (x1, y1, x2, y2),
                    'size': (width, height)
                })
        doc.close()
        return content_images

    def extract_complete_text(self, pdf_path, start_page, end_page):
        """Extract all text from the given page range, joining pages for multi-page questions."""
        doc = fitz.open(pdf_path)
        all_text = ""
        for page_num in range(start_page, end_page + 1):
            page = doc[page_num - 1]
            page_text = page.get_text()
            all_text += f"\n--- PAGE {page_num} ---\n{page_text}"
        doc.close()
        return all_text

    def extract_complete_text_from_pages(self, pdf_path, start_page, end_page):
        """Alias for extract_complete_text for compatibility."""
        return self.extract_complete_text(pdf_path, start_page, end_page)

    def extract_questions_from_complete_text(self, complete_text, content_images, pdf_filename):
        """
        Extract questions and options from the complete text using Gemini API, inserting <image> placeholders where images are detected.
        Handles multi-page questions and prepares for robust image mapping.
        """
        # Prepare image position map: {page: [(y1, y2, filename), ...]}
        image_map = {}
        for img in content_images:
            page = img['page']
            y1, y2 = img['position'][1], img['position'][3]
            image_map.setdefault(page, []).append((y1, y2, img['filename']))

        # Insert <image> placeholders into text based on image positions
        # (This is a heuristic: you may want to improve it for your layout)
        text_with_images = []
        page_blocks = re.split(r'--- PAGE (\d+) ---', complete_text)
        # page_blocks[0] is empty or header, then alternating page_num, page_text
        for i in range(1, len(page_blocks), 2):
            page_num = int(page_blocks[i])
            page_text = page_blocks[i+1]
            # For each image on this page, insert <image> at the closest line
            lines = page_text.splitlines()
            img_positions = sorted(image_map.get(page_num, []))
            for y1, y2, filename in img_positions:
                # Heuristic: insert <image> at the line closest to the vertical midpoint
                midpoint = (y1 + y2) // 2
                insert_idx = max(0, min(len(lines)-1, midpoint // 40))  # 40px per line approx
                lines.insert(insert_idx, '<image>')
            text_with_images.append(f'--- PAGE {page_num} ---\n' + '\n'.join(lines))
        joined_text = '\n'.join(text_with_images)

        # Gemini prompt for structured extraction
        prompt = f"""
You are an expert at extracting structured questions from exam papers. Extract all questions and their options from the following text. 

Rules:
- Each question should have: question text, optiona, optionb, optionc, optiond.
- If you see <image> in the question or option, treat it as an image placeholder.
- If a question spans multiple pages, combine the text into a single question.
- Ignore any page headers, footers, or logos.
- Output as a JSON list, where each item is:
  {{"question": ..., "optiona": ..., "optionb": ..., "optionc": ..., "optiond": ...}}

Text:
{joined_text}
"""
        try:
            response = self.model.generate_content(prompt)
            # Try to extract JSON from the response
            match = re.search(r'\[.*\]', response.text, re.DOTALL)
            if match:
                questions = json.loads(match.group(0))
            else:
                questions = []
        except Exception as e:
            logging.error(f"Gemini extraction failed: {e}")
            questions = []
        return questions

    def extract_questions_and_images_pipeline(self, pdf_path, start_page, end_page):
        """Pipeline: extract text, extract images, map images to questions, return questions list."""
        # Step 1: Extract all text
        complete_text = self.extract_complete_text(pdf_path, start_page, end_page)
        # Step 2: Extract all images using YOLO
        content_images = self.extract_content_images_yolo(pdf_path, start_page, end_page)
        # Step 3: Use Gemini to extract all questions (with <image> placeholders)
        questions = self.extract_questions_from_complete_text(complete_text, content_images, os.path.basename(pdf_path))
        # Step 4: Map images to questions (by page and vertical position)
        questions = self.map_images_to_questions_improved(questions, content_images, {})
        return questions

    def detect_bounding_boxes(self, image_path):
        """Enhanced YOLO detection for better accuracy."""
        try:
            from ultralytics import YOLO
            model = YOLO(r'src/yolo/best.pt')
            results = model.predict(
                source=image_path,
                imgsz=640,
                conf=0.01,  # Even lower confidence for maximum recall
                iou=0.1,    # Very low IoU to detect overlapping boxes
                task='detect',
                verbose=False
            )
            # Get all detected boxes regardless of class
            boxes = [tuple(map(int, box.xyxy[0].tolist()))
                    for box in results[0].boxes]
            return boxes
        except Exception as e:
            import logging
            logging.error(f"YOLO detection error: {e}")
            return []

    def map_images_to_questions_unified(self, questions, saved_images):
        """Unified image mapping - replace <image> tags with exact image filenames for Excel storage."""

        def normalize_language(lang):
            return lang.strip().title()

        def normalize_qno(qno):
            return str(qno).strip().lstrip('Q')

        def get_possible_keys(qno, lang):
            q = normalize_qno(qno)
            l = normalize_language(lang)
            return [f"{q}_{l}", f"Q{q}_{l}", q, f"Q{q}"]

        # Build image lookup table with exact filenames
        image_lookup = {}

        import logging
        logging.debug(f"DEBUG: Total saved images: {len(saved_images)}")
        for img_info in saved_images:
            try:
                question_num = normalize_qno(img_info.get('question', ''))
                language = normalize_language(img_info.get('language', ''))
                filename = img_info['filename']  # Use exact filename
                img_type = img_info.get('type', '').lower()

                possible_keys = get_possible_keys(question_num, language)

                for key in possible_keys:
                    if key not in image_lookup:
                        image_lookup[key] = {
                            'main_diagrams': [],
                            'options': {'A': [], 'B': [], 'C': [], 'D': []}
                        }

                    if img_type == 'main_diagram' or 'diagram' in filename.lower():
                        if filename not in image_lookup[key]['main_diagrams']:
                            image_lookup[key]['main_diagrams'].append(filename)
                    elif img_type.startswith('option_') or 'option' in filename.lower():
                        import re
                        match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                        if match:
                            option_letter = match.group(1).upper()
                            if filename not in image_lookup[key]['options'][option_letter]:
                                image_lookup[key]['options'][option_letter].append(filename)
            except Exception as e:
                logging.warning(f"WARNING: Skipping image entry due to error: {e} -> {img_info}")

        # Debug: Show lookup map
        logging.debug(f"DEBUG: Constructed image_lookup with keys: {list(image_lookup.keys())}")
        for k, v in image_lookup.items():
            logging.info(f"  {k} -> Main: {len(v['main_diagrams'])}, Options: {{opt: len(imgs) for opt, imgs in v['options'].items() if imgs}}")

        # Map images to questions with exact filenames
        for question in questions:
            qno = normalize_qno(question.get('qno', ''))
            language = normalize_language(question.get('language', ''))

            possible_keys = get_possible_keys(qno, language)

            question.setdefault('image_placeholder', "")
            # Fixed: Don't convert table_data to string here, keep it as processed list
            if 'table_data' not in question:
                question['table_data'] = []
            question.setdefault('type_of_question', "MCQ")
            question.setdefault('mark', "1")

            img_data = None
            matched_key = None
            for key in possible_keys:
                if key in image_lookup:
                    img_data = image_lookup[key]
                    matched_key = key
                    break

            logging.debug(f"DEBUG: Processing Q{qno}_{language} → Matched key: {matched_key}")

            # Map main diagram with exact filename
            if img_data:
                question_text = question.get('question', '')
                if img_data['main_diagrams']:
                    main_image = img_data['main_diagrams'][0]  # Exact filename
                    if '<image>' in question_text:
                        question['question'] = question_text.replace('<image>', '', 1)
                    question['image_placeholder'] = main_image  # Store exact filename

                # Map options with exact filenames
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        available_images = img_data['options'].get(opt_letter, [])

                        logging.debug(f"DEBUG: Q{qno}_{language} Option {opt_letter} → Images: {available_images}")

                        if available_images:
                            exact_filename = available_images[0]  # Use exact filename
                            if opt_text.lower() == '<image>':
                                question[opt_key] = exact_filename  # Store exact filename
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', exact_filename)
                        else:
                            if opt_text.lower() == '<image>':
                                question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')
            else:
                # No match found for this question
                logging.debug(f"DEBUG: No images found for Q{qno}_{language}")
                if '<image>' in question.get('question', ''):
                    question['image_placeholder'] = "IMAGE_MISSING"
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        if opt_text.lower() == '<image>':
                            question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                        elif '<image>' in opt_text:
                            question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')

        return questions

    def save_to_excel_with_structured_format(self, questions, excel_path):
        """Save questions to Excel with integrated LaTeX format, without blooms_level and type_of_question columns."""
        try:
            import pandas as pd
            from openpyxl import Workbook
            from openpyxl.styles import Alignment, Font
            # Define Excel columns for integrated format (removed blooms_level and type_of_question)
            excel_columns = [
                'qno', 'language', 'question', 'optiona', 'optionb', 'optionc', 'optiond', 'mark'
            ]
            # Convert structured format to JSON strings for Excel storage
            for question in questions:
                for col in excel_columns:
                    if col not in question:
                        if col == 'question':
                            question[col] = {}
                        elif col in ['optiona', 'optionb', 'optionc', 'optiond']:
                            question[col] = {"text": "", "img": ""}
                        else:
                            question[col] = ""
                # Convert structured fields to JSON strings - question field only has text, image, tables
                if isinstance(question['question'], dict):
                    cleaned_question = {
                        "text": question['question'].get('text', ''),
                        "image": question['question'].get('image', []),
                        "tables": question['question'].get('tables', {})
                    }
                    question['question'] = json.dumps(cleaned_question, ensure_ascii=False)
                # Convert option fields to JSON strings - only text and img
                for opt_key in ['optiona', 'optionb', 'optionc', 'optiond']:
                    if isinstance(question[opt_key], dict):
                        cleaned_option = {
                            "text": question[opt_key].get('text', ''),
                            "img": question[opt_key].get('img', '')
                        }
                        question[opt_key] = json.dumps(cleaned_option, ensure_ascii=False)
            # Create DataFrame and ensure all columns exist
            df = pd.DataFrame(questions)
            for col in excel_columns:
                if col not in df.columns:
                    df[col] = ""
            df = df[excel_columns]
            # Create workbook manually for better Unicode control
            wb = Workbook()
            ws = wb.active
            ws.title = "Questions_Integrated_LaTeX"
            # Add headers
            for col_idx, header in enumerate(excel_columns, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                cell.font = Font(bold=True)
            # Add data rows with Unicode preservation
            for row_idx, question in enumerate(questions, 2):
                for col_idx, col_name in enumerate(excel_columns, 1):
                    cell_value = question.get(col_name, "")
                    if isinstance(cell_value, str):
                        ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    else:
                        ws.cell(row=row_idx, column=col_idx, value=str(cell_value))
            # Set column widths for better readability
            column_widths = {
                'A': 8,   # qno
                'B': 12,  # language  
                'C': 120, # question (increased for JSON content with integrated LaTeX)
                'D': 100, # optiona (increased for JSON with integrated LaTeX)
                'E': 100, # optionb
                'F': 100, # optionc
                'G': 100, # optiond
                'H': 8    # mark
            }
            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width
            # Set text wrapping for JSON columns
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical='top')
            wb.save(excel_path)
            logging.info(f"Excel file saved with integrated LaTeX format: {excel_path}")
            return True
        except Exception as e:
            logging.error(f"Error saving to Excel with integrated LaTeX format: {e}")
            import traceback
            traceback.print_exc()
            return False
