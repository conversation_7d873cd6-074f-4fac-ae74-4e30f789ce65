"""
Modern Document Layout Analysis using AI Models
Uses EasyOCR + Computer Vision for document understanding
"""

import logging
import re
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import cv2
import numpy as np
from PIL import Image
import easyocr
from tqdm import tqdm

try:
    from .config import PipelineConfig
except ImportError:
    from config import PipelineConfig

class DocumentLayoutAnalyzer:
    """Analyze document layout using AI models"""
    
    def __init__(self, config: PipelineConfig = None):
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
        
        # Initialize OCR reader
        self.logger.info("Initializing EasyOCR...")
        self.ocr_reader = easyocr.Reader(['en'], gpu=False)  # Set gpu=True if you have CUDA
        self.logger.info("EasyOCR initialized successfully")
    
    def _setup_logger(self):
        """Setup logging"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_page(self, image_path: Path) -> Dict:
        """
        Analyze a single page image for document structure
        
        Args:
            image_path: Path to the page image
            
        Returns:
            Dictionary with detected blocks and structure
        """
        self.logger.info(f"Analyzing page: {image_path.name}")
        
        # Load image
        image = cv2.imread(str(image_path))
        pil_image = Image.open(image_path)
        
        # Extract text with bounding boxes using EasyOCR
        ocr_results = self.ocr_reader.readtext(str(image_path))
        
        # Detect different types of content blocks
        text_blocks = self._extract_text_blocks(ocr_results, image)
        question_blocks = self._detect_questions(text_blocks)
        option_blocks = self._detect_options(text_blocks)
        figure_blocks = self._detect_figures(image, text_blocks)
        table_blocks = self._detect_tables(text_blocks)
        
        # Check if this page contains solutions (to skip)
        has_solutions = self._detect_solutions(text_blocks)
        
        # Filter out logos
        filtered_figures = self._filter_logos(figure_blocks, pil_image)
        
        return {
            "page_path": str(image_path),
            "has_solutions": has_solutions,
            "text_blocks": text_blocks,
            "question_blocks": question_blocks,
            "option_blocks": option_blocks,
            "figure_blocks": filtered_figures,
            "table_blocks": table_blocks,
            "image_size": pil_image.size
        }
    
    def _extract_text_blocks(self, ocr_results: List, image: np.ndarray) -> List[Dict]:
        """Extract text blocks with bounding boxes"""
        text_blocks = []
        
        for detection in ocr_results:
            bbox, text, confidence = detection
            
            if confidence < 0.5:  # Skip low confidence detections
                continue
            
            # Convert bbox to standard format [x1, y1, x2, y2]
            points = np.array(bbox)
            x1, y1 = points.min(axis=0)
            x2, y2 = points.max(axis=0)
            
            text_blocks.append({
                "text": text.strip(),
                "bbox": [int(x1), int(y1), int(x2), int(y2)],
                "confidence": confidence,
                "type": "text"  # Will be refined later
            })
        
        return text_blocks
    
    def _detect_questions(self, text_blocks: List[Dict]) -> List[Dict]:
        """Detect question blocks"""
        question_blocks = []
        
        for block in text_blocks:
            text = block["text"]
            
            # Check for question patterns
            for pattern in self.config.QUESTION_PATTERNS:
                if re.match(pattern, text):
                    block_copy = block.copy()
                    block_copy["type"] = "question_number"
                    question_blocks.append(block_copy)
                    break
            
            # Look for question text (longer text blocks near question numbers)
            if len(text) > 20 and any(word in text.lower() for word in 
                                    ["the", "what", "which", "how", "if", "when", "where"]):
                block_copy = block.copy()
                block_copy["type"] = "question_text"
                question_blocks.append(block_copy)
        
        return question_blocks
    
    def _detect_options(self, text_blocks: List[Dict]) -> List[Dict]:
        """Detect option blocks"""
        option_blocks = []
        
        for block in text_blocks:
            text = block["text"]
            
            # Check for option patterns
            for i, pattern in enumerate(self.config.OPTION_PATTERNS, 1):
                if re.search(pattern, text):
                    block_copy = block.copy()
                    block_copy["type"] = "option"
                    block_copy["option_number"] = i
                    option_blocks.append(block_copy)
                    break
        
        return option_blocks
    
    def _detect_figures(self, image: np.ndarray, text_blocks: List[Dict]) -> List[Dict]:
        """Detect figure/image regions"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Find contours for potential figures
        edges = cv2.Canny(gray, 50, 150)
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        figure_blocks = []
        
        for contour in contours:
            # Filter by area
            area = cv2.contourArea(contour)
            if area < 1000:  # Skip small contours
                continue
            
            # Get bounding box
            x, y, w, h = cv2.boundingRect(contour)
            
            # Check if this region has minimal text (likely a figure)
            text_in_region = self._count_text_in_region([x, y, x+w, y+h], text_blocks)
            
            if text_in_region < 3:  # Likely a figure
                figure_blocks.append({
                    "type": "figure",
                    "bbox": [x, y, x+w, y+h],
                    "area": area
                })
        
        return figure_blocks
    
    def _detect_tables(self, text_blocks: List[Dict]) -> List[Dict]:
        """Detect table structures"""
        table_blocks = []
        
        # Look for table indicators
        table_keywords = ["list-i", "list-ii", "column", "match", "table"]
        
        for block in text_blocks:
            text = block["text"].lower()
            if any(keyword in text for keyword in table_keywords):
                block_copy = block.copy()
                block_copy["type"] = "table"
                table_blocks.append(block_copy)
        
        return table_blocks
    
    def _detect_solutions(self, text_blocks: List[Dict]) -> bool:
        """Check if page contains solutions (to skip)"""
        all_text = " ".join([block["text"] for block in text_blocks]).lower()
        
        for pattern in self.config.SOLUTION_PATTERNS:
            if re.search(pattern.lower(), all_text):
                return True
        
        return False
    
    def _filter_logos(self, figure_blocks: List[Dict], image: Image.Image) -> List[Dict]:
        """Filter out known logos"""
        filtered_blocks = []
        
        for block in figure_blocks:
            x1, y1, x2, y2 = block["bbox"]
            width = x2 - x1
            height = y2 - y1
            
            # Check against known logo sizes
            is_logo = False
            for logo_w, logo_h in self.config.LOGO_SIZES:
                if abs(width - logo_w) < 20 and abs(height - logo_h) < 20:
                    is_logo = True
                    break
            
            if not is_logo:
                filtered_blocks.append(block)
        
        return filtered_blocks
    
    def _count_text_in_region(self, bbox: List[int], text_blocks: List[Dict]) -> int:
        """Count text blocks overlapping with a region"""
        x1, y1, x2, y2 = bbox
        count = 0
        
        for block in text_blocks:
            bx1, by1, bx2, by2 = block["bbox"]
            
            # Check for overlap
            if not (bx2 < x1 or bx1 > x2 or by2 < y1 or by1 > y2):
                count += 1
        
        return count


def test_analyzer():
    """Test the document analyzer"""
    config = PipelineConfig()
    analyzer = DocumentLayoutAnalyzer(config)
    
    # Test with a sample image (you'll need to run PDF converter first)
    temp_dir = config.TEMP_DIR
    if temp_dir.exists():
        image_files = list(temp_dir.glob("*.png"))
        if image_files:
            result = analyzer.analyze_page(image_files[0])
            print(f"Analysis result: {json.dumps(result, indent=2)}")
        else:
            print("No images found. Run PDF converter first.")
    else:
        print("Temp directory not found. Run PDF converter first.")


if __name__ == "__main__":
    test_analyzer()
