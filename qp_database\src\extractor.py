import os
import time
import fitz
from pathlib import Path
from .base import PDFProcessor
from ultralytics import YOLO
import logging


class StartProcessing(PDFProcessor):

    def __init__(self, input_dir, excel_output_dir, images_dir):
        super().__init__(input_dir, excel_output_dir, images_dir)
        # Convert to absolute paths
        self.input_dir = str(Path(input_dir).resolve())
        self.excel_output_dir = str(Path(excel_output_dir).resolve())
        self.images_dir = str(Path(images_dir).resolve())

        # Create output directories
        os.makedirs(self.excel_output_dir, exist_ok=True)
        os.makedirs(self.images_dir, exist_ok=True)

        # Check input directory exists
        if not os.path.isdir(self.input_dir):
            logging.error(f"Input directory does not exist: {self.input_dir}")
            raise FileNotFoundError(f"Input directory does not exist: {self.input_dir}")

    #extract content from question paper
    def extract_all_questions_from_pdf(self, limit=30):
        start_time = time.time()

        # Only process clgdunia.pdf from the project root
        project_root = Path(__file__).parent.parent
        pdf_file = project_root / "clgdunia.pdf"
        if not pdf_file.exists():
            logging.error(f"PDF file does not exist: {pdf_file}")
            print(f"PDF file does not exist: {pdf_file}")
            return

        # Initialize YOLO model for image detection
        yolo_path = Path(__file__).parent / "yolo" / "best.pt"
        if not yolo_path.exists():
            logging.error(f"YOLO model file not found: {yolo_path}")
            raise FileNotFoundError(f"YOLO model file not found: {yolo_path}")
        self.yolo_model = YOLO(str(yolo_path))

        try:
            doc = fitz.open(str(pdf_file.resolve()))
            total_pages = len(doc)
            doc.close()

            # Process all pages except the first (usually instructions)
            start_page = 2
            end_page = total_pages

            print(f"📄 PDF has {total_pages} pages, processing pages {start_page} to {end_page}")

            # Extract images from the PDF (ignoring logos)
            content_images = self.extract_content_images_yolo(str(pdf_file.resolve()), start_page, end_page)
            complete_text = self.extract_complete_text_from_pages(str(pdf_file.resolve()), start_page, end_page)

            # Extract questions from the complete text
            questions = self.extract_questions_from_complete_text(complete_text, content_images, pdf_file.name)

            # Map images to questions (handles multi-page questions)
            questions = self.map_images_to_questions_unified(questions, content_images)

            # Save to Excel
            excel_path = str(Path(self.excel_output_dir) / f"{pdf_file.stem}_questions.xlsx")
            os.makedirs(self.excel_output_dir, exist_ok=True)
            questions = questions[:limit]
            success = self.save_to_excel_with_structured_format(questions, excel_path)

            if success and len(questions) <= 30:
                print(f"✅ Successfully processed: {pdf_file.name}")
            else:
                print(f"❌ Failed to process: {pdf_file.name}")

        except Exception as e:
            logging.error(f"Error reading PDF {pdf_file.name}: {e}")
            print(f"Error reading PDF {pdf_file.name}: {e}")
            success = self.save_to_excel_with_structured_format(questions, excel_path)

            if success and len(questions) <= 30:  # Limit to first 30 questions
                print(f"✅ Successfully processed: {pdf_file.name}")
            else:
                print(f"❌ Failed to process: {pdf_file.name}")

        except Exception as e:
            logging.error(f"Error reading PDF {pdf_file.name}: {e}")
            print(f"Error reading PDF {pdf_file.name}: {e}")


