import os
import re
import json
import glob
import time
import shutil
import logging
import pandas as pd
import google.generativeai as genai
from dotenv import load_dotenv
import fitz  # PyMuPDF
import cv2
import tempfile
from datetime import datetime
from pathlib import Path
from ultralytics import YOLO
from openai import OpenAI
import yaml
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils.dataframe import dataframe_to_rows
import base64

# Load environment variables
load_dotenv()

class PDFProcessor:
    def __init__(self, input_dir, output_dir, image_dir):
        # Convert to absolute paths
        base_dir = Path(__file__).parent.parent
        self.input_dir = str(base_dir / input_dir)
        self.output_dir = str(base_dir / output_dir)
        self.image_dir = str(base_dir / image_dir)

        # Create required directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)

        # Initialize Gemini API
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-2.0-flash")
        self.response_counter = 1

        # Initialize OpenAI client for YOLO functionality
        self.openai_client = OpenAI(
            api_key=os.getenv("GEMINI_API_KEY"),
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )

        # Track processed images to avoid duplicates - enhanced tracking
        self.processed_image_signatures = {}
        self.question_image_counter = {}
        self.global_image_signatures = set()  # Track all processed image signatures globally

    def compute_image_signature(self, image_array):
        """Compute enhanced signature of cropped image to detect exact duplicates."""
        try:
            import hashlib
            # Convert to grayscale and resize for comparison
            gray = cv2.cvtColor(image_array, cv2.COLOR_BGR2GRAY)
            resized = cv2.resize(gray, (64, 64))  # Increased resolution for better detection
            
            # Compute multiple features for better duplicate detection
            pixel_hash = hashlib.md5(resized.tobytes()).hexdigest()
            
            # Add histogram-based signature
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist_hash = hashlib.md5(hist.tobytes()).hexdigest()
            
            # Combine signatures
            combined_signature = f"{pixel_hash}_{hist_hash}"
            return combined_signature
        except Exception as e:
            logging.error(f"Error computing signature: {e}")
            return None

    def is_duplicate_crop(self, image_array, question_id):
        """Enhanced duplicate detection with global tracking."""
        signature = self.compute_image_signature(image_array)
        if signature is None:
            return False
        
        # Check global duplicates first
        if signature in self.global_image_signatures:
            logging.info(f"Global duplicate detected for signature: {signature[:16]}...")
            return True
        
        # Create unique key for question + signature
        key = f"{question_id}_{signature}"
        
        if key in self.processed_image_signatures:
            return True
        
        # Mark both question-specific and global
        self.processed_image_signatures[key] = True
        self.global_image_signatures.add(signature)
        return False

    def validate_image_content(self, image_path, box_coords):
        """Enhanced validation to distinguish between actual images and mathematical content."""
        try:
            img = cv2.imread(image_path)
            x1, y1, x2, y2 = box_coords
            cropped = img[y1:y2, x1:x2]
            
            # Check if image is too small or empty (lowered threshold)
            if cropped.size == 0 or cropped.shape[0] < 10 or cropped.shape[1] < 10:
                return False
            
            # Save temporary cropped image
            temp_path = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
            temp_path.close()
            cv2.imwrite(temp_path.name, cropped)
            
            with open(temp_path.name, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            prompt = """
            Analyze this cropped region to categorize content:

            Return "VISUAL" if it contains:
            - Actual photographs, illustrations, drawings
            - Real diagrams (flowcharts, circuit diagrams, anatomical drawings)
            - Charts, graphs with plotted data points
            - Maps, geometric shapes with measurements
            - Visual option choices (actual pictures as A, B, C, D)
            - Tables with complex layouts or visual elements
            - Any visual content that should be preserved as an image

            Return "MATHEMATICAL" if it contains ONLY:
            - Simple mathematical equations or formulas
            - Basic mathematical symbols

            Return "TEXT" if it contains ONLY:
            - Plain text paragraphs
            - Simple option letters with basic text
            - Empty or mostly blank areas

            When in doubt, prefer "VISUAL" to preserve important content.
            
            Respond with EXACTLY ONE WORD: VISUAL, MATHEMATICAL, or TEXT
            """

            response = self.openai_client.chat.completions.create(
                model="gemini-1.5-flash",
                messages=[
                    {"role": "system", "content": "You are an expert at distinguishing visual content from mathematical content and text."},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ],
                max_tokens=5
            )
            
            os.remove(temp_path.name)
            result = response.choices[0].message.content.strip().upper()
            return result == "VISUAL"  # Only true visual content should be processed as images
            
        except Exception as e:
            logging.error(f"Error validating content: {e}")
            return False

    def identify_and_classify_images(self, image_path, boxes):
        """Enhanced classification that separates visual content from mathematical content."""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            box_list_str = "\n".join([f"Box {i+1}: coordinates {box}" for i, box in enumerate(boxes)])
            prompt = f"""
            Analyze this bilingual question paper page (English + Tamil) and classify each bounding box.
            
            CRITICAL REQUIREMENTS:
            1. Each question appears TWICE (English first, then Tamil)
            2. Questions are numbered Q1, Q2, Q3... or 1, 2, 3...
            3. ONLY classify boxes with ACTUAL VISUAL content (photos, illustrations, real diagrams - NOT mathematical content)
            4. MATHEMATICAL CONTENT (equations, formulas, matrices) should NOT be classified here - they will be extracted as LaTeX
            5. AVOID duplicate classifications - each unique visual should be classified only once per question
            6. Strictly exclude: mathematical equations, formulas, matrices, chemical equations, mathematical graphs
            7. Strictly exclude : cropped version of existing images
            8. Strictly exclude : bounding box of partial part of same image again
            9. Never skip any kind of images
            For each valid VISUAL box (NOT mathematical content), determine:
            
            A) IMAGE TYPE:
            - "main_diagram": Primary visual diagram/photo/illustration (NOT mathematical formulas)
            - "option_A": Visual option A (actual image/photo/diagram, NOT text or math)
            - "option_B": Visual option B (actual image/photo/diagram, NOT text or math)  
            - "option_C": Visual option C (actual image/photo/diagram, NOT text or math)
            - "option_D": Visual option D (actual image/photo/diagram, NOT text or math)
            - "figure": Additional visual figures/photos/illustrations (NOT mathematical)
            
            B) QUESTION MAPPING:
            - Exact question number (1, 2, 3, etc.)
            - Language version (English or Tamil)
            
            C) STRICT VALIDATION:
            - SKIP all mathematical content (equations, formulas, matrices, etc.)
            - SKIP boxes containing only text or mathematical expressions
            - ONLY include actual photographs, illustrations, or real visual diagrams
            - AVOID classifying mathematical graphs, coordinate systems, or formula diagrams
            
            RESPONSE FORMAT (one line per valid visual box):
            Box <number>: <image_type> - Q<question_number>_<language>
            
            EXAMPLES:
            Box 3: main_diagram - Q1_English  (only if it's a photo/illustration, NOT a formula)
            Box 5: option_A - Q2_English      (only if it's a visual choice, NOT mathematical)
            
            Be EXTREMELY strict: Mathematical formulas, equations, matrices should be extracted as LaTeX text, not treated as images.
            If no valid VISUAL boxes exist, respond with "No visual content"
            Strictly never include cropped version of existing image again this must be followed very strictly
            Detected boxes:
            {box_list_str}
            Strictly never include cropped version of existing image again this must be followed very strictly
            """

            response = self.openai_client.chat.completions.create(
                model="gemini-1.5-flash",
                messages=[
                    {"role": "system", "content": "You are an expert at identifying true visual content while excluding mathematical formulas and equations."},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ],
                max_tokens=400
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"Error classifying images: {e}")
            return ""

    def parse_image_classification(self, response):
        """Parse the image classification response with exact format."""
        valid_images = []
        
        if "no visual content" in response.lower():
            return valid_images
        
        lines = response.strip().split('\n')
        for line in lines:
            line = line.strip()
            # Match: Box <number>: <type> - Q<number>_<language>
            match = re.search(r"Box\s+(\d+):\s*([^-]+?)\s*-\s*Q?(\d+)_(\w+)", line, re.IGNORECASE)
            if match:
                box_idx = int(match.group(1)) - 1  # Convert to 0-based
                image_type = match.group(2).strip()
                q_number = match.group(3)
                language = match.group(4)
                valid_images.append((box_idx, image_type, q_number, language))
        
        return valid_images

    def generate_correct_filename(self, image_type, q_number, language, page_num, pdf_name):
        """Generate correct filename based on PDF name and image type."""
        # Extract clean PDF name without extension
        clean_pdf_name = os.path.splitext(os.path.basename(pdf_name))[0]
        # Remove any special characters and replace with underscore
        clean_pdf_name = re.sub(r'[^a-zA-Z0-9_]', '_', clean_pdf_name)
        
        if image_type == "main_diagram":
            return f"{clean_pdf_name}_Q{q_number}_{language}_diagram_page{page_num}.jpg"
        elif image_type.startswith("option_"):
            option_letter = image_type.split("_")[1].upper()  # A, B, C, D
            return f"{clean_pdf_name}_Q{q_number}_{language}_option{option_letter}_page{page_num}.jpg"
        elif image_type == "figure":
            return f"{clean_pdf_name}_Q{q_number}_{language}_figure_page{page_num}.jpg"
        else:
            clean_type = re.sub(r'[^a-zA-Z0-9_]', '_', image_type)
            return f"{clean_pdf_name}_Q{q_number}_{language}_{clean_type}_page{page_num}.jpg"

    def process_and_save_classified_images(self, image_path, boxes, classified_images, page_num, pdf_name):
        """Process and save images with PDF name-based filenames."""
        saved_images = []
        
        if not classified_images:
            return saved_images

        img = cv2.imread(image_path)
        
        for box_idx, image_type, q_number, language in classified_images:
            try:
                logging.info(f"🔍 Processing box {box_idx+1}: {image_type} for Q{q_number}_{language}")

                if box_idx >= len(boxes):
                    logging.info(f"❌ Invalid box index {box_idx}, skipping")
                    continue

                x1, y1, x2, y2 = boxes[box_idx]
                logging.info(f"📐 Box coordinates: ({x1}, {y1}, {x2}, {y2})")

                # Validate coordinates
                if (x1 >= x2 or y1 >= y2 or x1 < 0 or y1 < 0 or
                    x2 > img.shape[1] or y2 > img.shape[0]):
                    logging.info(f"❌ Invalid coordinates for box {box_idx}, skipping")
                    continue

                # Validate content is actually visual
                logging.info(f"🔍 Validating image content for box {box_idx+1}...")
                if not self.validate_image_content(image_path, (x1, y1, x2, y2)):
                    logging.info(f"❌ Box {box_idx+1} contains text content, skipping")
                    continue

                logging.info(f"✅ Box {box_idx+1} passed validation, proceeding to save...")
                
                # Crop image
                cropped = img[y1:y2, x1:x2]
                
                # Enhanced duplicate check with global tracking
                question_id = f"Q{q_number}_{language}_{image_type}"
                if self.is_duplicate_crop(cropped, question_id):
                    logging.info(f"Duplicate crop detected for {question_id}, skipping")
                    continue
                
                # Generate correct filename with PDF name
                filename = self.generate_correct_filename(image_type, q_number, language, page_num, pdf_name)
                image_save_path = os.path.join(self.image_dir, filename)
                
                # Save image
                cv2.imwrite(image_save_path, cropped)
                
                saved_images.append({
                    'path': image_save_path,
                    'filename': filename,
                    'type': image_type,
                    'question': q_number,
                    'language': language,
                    'box_idx': box_idx
                })
                
                logging.info(f"Saved: {filename}")
                
            except Exception as e:
                logging.error(f"Error processing box {box_idx+1}: {e}")
                continue
        
        return saved_images

    def determine_question_range(self, page_num, questions_per_page=5):
        """Determine expected question range for a given page."""
        # Questions start from page 2, so page 2 = Q1-Q5, page 3 = Q6-Q10, etc.
        if page_num < 2:
            # Page 1 typically has no questions or intro content
            return 0, 0

        start_qno = (page_num - 2) * questions_per_page + 1  # Page 2 starts with Q1
        end_qno = start_qno + questions_per_page - 1
        return start_qno, end_qno

    def clean_json_response_unicode_safe(self, response_text):
        """Clean JSON response while preserving Unicode characters."""
        try:
            # Remove code block markers
            response_text = response_text.strip()
            if response_text.startswith('```'):
                response_text = response_text[7:]
            elif response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            response_text = response_text.strip()
            
            # Fix common JSON formatting issues while preserving Unicode
            # Remove any trailing commas before closing brackets/braces
            response_text = re.sub(r',(\s*[}$$])', r'\1', response_text)
            
            # Ensure proper JSON array format
            if not response_text.startswith('['):
                response_text = '[' + response_text
            if not response_text.endswith(']'):
                response_text = response_text + ']'
            
            return response_text
        except Exception as e:
            logging.error(f"Error cleaning JSON response: {e}")
            return response_text

    def validate_and_clean_questions(self, questions):
        """Validate and clean extracted questions while preserving Unicode."""
        cleaned_questions = []
        
        for q in questions:
            if not isinstance(q, dict):
                continue
                
            # Ensure required fields exist
            cleaned_q = {
                'qno': str(q.get('qno', '')).strip(),
                'language': str(q.get('language', 'English')).strip(),
                'question': str(q.get('question', '')).strip(),
                'optiona': str(q.get('optiona', '')).strip(),
                'optionb': str(q.get('optionb', '')).strip(),
                'optionc': str(q.get('optionc', '')).strip(),
                'optiond': str(q.get('optiond', '')).strip(),
                'table_data': q.get('table_data', []),
                'type_of_question': str(q.get('type_of_question', 'MCQ')).strip(),
                'mark': str(q.get('mark', '1')).strip()
            }
            
            # Skip if no question text
            if not cleaned_q['question']:
                continue
                
            # Clean question number
            cleaned_q['qno'] = re.sub(r'^[Qq]', '', cleaned_q['qno'])
            
            # Validate language
            if cleaned_q['language'].lower() not in ['english', 'tamil']:
                cleaned_q['language'] = 'English'
            else:
                cleaned_q['language'] = cleaned_q['language'].title()
            
            cleaned_questions.append(cleaned_q)
        
        return cleaned_questions

    def validate_latex_syntax(self, question):
        """Validate and fix LaTeX syntax issues with proper delimiters."""
        fields_to_check = ['question', 'optiona', 'optionb', 'optionc', 'optiond']
        
        for field in fields_to_check:
            if field in question and question[field]:
                content = question[field]
                
                # Fix triple dollar signs to double (common error)
                content = re.sub(r'\$\$\$([^$]+)\$\$\$', r'$$\1$$', content)
                
                # Don't modify already properly formatted display math ($$...$$)
                # Only fix standalone single dollars that aren't part of display math
                # Use negative lookbehind and lookahead to avoid touching $$
                content = re.sub(r'(?<!\$)\$([^$\n]+)\$(?!\$)', r'$$\1$$', content)
                
                # Fix common LaTeX command formatting
                content = re.sub(r'\\frac\s*\{([^}]+)\}\s*\{([^}]+)\}', r'\\frac{\1}{\2}', content)
                content = re.sub(r'\\sqrt\s*\{([^}]+)\}', r'\\sqrt{\1}', content)
                content = re.sub(r'\\sum_\s*\{([^}]+)\}', r'\\sum_{\1}', content)
                content = re.sub(r'\\int_\s*\{([^}]+)\}', r'\\int_{\1}', content)
                content = re.sub(r'\\lim_\s*\{([^}]+)\}', r'\\lim_{\1}', content)
                
                # Fix subscripts and superscripts
                content = re.sub(r'([a-zA-Z])_([a-zA-Z0-9]+)(?![{}])', r'\1_{\2}', content)
                content = re.sub(r'([a-zA-Z])\^([a-zA-Z0-9]+)(?![{}])', r'\1^{\2}', content)
                
                # Replace Unicode mathematical symbols with LaTeX equivalents
                unicode_to_latex = {
                    'θ': '\\theta',
                    'π': '\\pi',
                    'α': '\\alpha',
                    'β': '\\beta',
                    'γ': '\\gamma',
                    'δ': '\\delta',
                    'ε': '\\epsilon',
                    'λ': '\\lambda',
                    'μ': '\\mu',
                    'σ': '\\sigma',
                    'φ': '\\phi',
                    'ψ': '\\psi',
                    'ω': '\\omega',
                    '∞': '\\infty',
                    '±': '\\pm',
                    '∓': '\\mp',
                    '≈': '\\approx',
                    '≠': '\\neq',
                    '≥': '\\geq',
                    '≤': '\\leq',
                    '»': '\\gg',
                    '«': '\\ll',
                    '∫': '\\int',
                    '∑': '\\sum',
                    '∏': '\\prod',
                    '√': '\\sqrt',
                    '∂': '\\partial',
                    '∇': '\\nabla',
                    '∆': '\\Delta',
                    '∈': '\\in',
                    '∉': '\\notin',
                    '⊂': '\\subset',
                    '⊃': '\\supset',
                    '∪': '\\cup',
                    '∩': '\\cap',
                    '→': '\\rightarrow',
                    '←': '\\leftarrow',
                    '↔': '\\leftrightarrow',
                    '⇒': '\\Rightarrow',
                    '⇐': '\\Leftarrow',
                    '⇔': '\\Leftrightarrow'
                }
                
                for unicode_char, latex_cmd in unicode_to_latex.items():
                    content = content.replace(unicode_char, latex_cmd)
                
                question[field] = content
        
        return question

    def process_table_data(self, table_data):
        """Process table data to ensure proper list of lists format [['a','b'],['c','d']]."""
        if not table_data:
            return []
        
        # If it's already a proper list of lists
        if isinstance(table_data, list):
            processed_table = []
            for row in table_data:
                if isinstance(row, list):
                    # Already a list - convert all elements to strings
                    processed_row = [str(cell) for cell in row]
                    processed_table.append(processed_row)
                elif isinstance(row, dict):
                    # Convert dict to list of values
                    processed_row = [str(value) for value in row.values()]
                    processed_table.append(processed_row)
                elif isinstance(row, set):
                    # Convert set to list
                    processed_row = [str(item) for item in row]
                    processed_table.append(processed_row)
                elif isinstance(row, (str, int, float)):
                    # Single value - make it a single-item list
                    processed_table.append([str(row)])
                else:
                    # Try to convert to string and make single-item list
                    processed_table.append([str(row)])
            return processed_table
        
        # If it's a string representation, try to parse it
        elif isinstance(table_data, str):
            if not table_data.strip():
                return []
            try:
                # Try to evaluate as Python literal (safe for lists)
                import ast
                parsed = ast.literal_eval(table_data)
                return self.process_table_data(parsed)  # Recursive call to handle parsed data
            except:
                # If parsing fails, return as single cell table
                return [[table_data]]
        
        # For any other type, convert to string and return as single cell
        else:
            return [[str(table_data)]]

    def clean_json_response_with_latex(self, response_text):
        """Clean JSON response while preserving LaTeX mathematical content."""
        try:
            # Remove code block markers
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            elif response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            response_text = response_text.strip()
            
            # Preserve LaTeX content by temporarily replacing it
            latex_patterns = []
            latex_counter = 0
            
            # Find and preserve $$....$$ patterns (display math)
            def preserve_latex(match):
                nonlocal latex_counter
                placeholder = f"__LATEX_PLACEHOLDER_{latex_counter}__"
                latex_patterns.append((placeholder, match.group(0)))
                latex_counter += 1
                return placeholder
            
            # Preserve LaTeX expressions (display math only)
            response_text = re.sub(r'\$\$[^$]*?\$\$', preserve_latex, response_text, flags=re.DOTALL)
            
            # Fix common JSON formatting issues
            response_text = re.sub(r',(\s*[}\]])', r'\1', response_text)
            
            # Ensure proper JSON array format
            if not response_text.startswith('['):
                response_text = '[' + response_text
            if not response_text.endswith(']'):
                response_text = response_text + ']'
            
            # Restore LaTeX content
            for placeholder, original in latex_patterns:
                response_text = response_text.replace(placeholder, original)
            
            return response_text
        except Exception as e:
            logging.error(f"Error cleaning JSON response with LaTeX: {e}")
            return response_text

    def map_images_to_questions_unified(self, questions, saved_images):
        """Unified image mapping - replace <image> tags with exact image filenames for Excel storage."""

        def normalize_language(lang):
            return lang.strip().title()

        def normalize_qno(qno):
            return str(qno).strip().lstrip('Q')

        def get_possible_keys(qno, lang):
            q = normalize_qno(qno)
            l = normalize_language(lang)
            return [f"{q}_{l}", f"Q{q}_{l}", q, f"Q{q}"]

        # Build image lookup table with exact filenames
        image_lookup = {}

        logging.debug(f"DEBUG: Total saved images: {len(saved_images)}")
        for img_info in saved_images:
            try:
                question_num = normalize_qno(img_info['question'])
                language = normalize_language(img_info['language'])
                filename = img_info['filename']  # Use exact filename
                img_type = img_info['type'].lower()

                possible_keys = get_possible_keys(question_num, language)

                for key in possible_keys:
                    if key not in image_lookup:
                        image_lookup[key] = {
                            'main_diagrams': [],
                            'options': {'A': [], 'B': [], 'C': [], 'D': []}
                        }

                    if img_type == 'main_diagram' or 'diagram' in filename.lower():
                        if filename not in image_lookup[key]['main_diagrams']:
                            image_lookup[key]['main_diagrams'].append(filename)
                    elif img_type.startswith('option_') or 'option' in filename.lower():
                        match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                        if match:
                            option_letter = match.group(1).upper()
                            if filename not in image_lookup[key]['options'][option_letter]:
                                image_lookup[key]['options'][option_letter].append(filename)
            except Exception as e:
                logging.warning(f"WARNING: Skipping image entry due to error: {e} -> {img_info}")

        # Debug: Show lookup map
        logging.debug(f"DEBUG: Constructed image_lookup with keys: {list(image_lookup.keys())}")
        for k, v in image_lookup.items():
            logging.info(f"  {k} -> Main: {len(v['main_diagrams'])}, Options: { {opt: len(imgs) for opt, imgs in v['options'].items() if imgs} }")

        # Map images to questions with exact filenames
        for question in questions:
            qno = normalize_qno(question.get('qno', ''))
            language = normalize_language(question.get('language', ''))

            possible_keys = get_possible_keys(qno, language)

            question.setdefault('image_placeholder', "")
            # Fixed: Don't convert table_data to string here, keep it as processed list
            if 'table_data' not in question:
                question['table_data'] = []
            question.setdefault('type_of_question', "MCQ")
            question.setdefault('mark', "1")

            img_data = None
            matched_key = None
            for key in possible_keys:
                if key in image_lookup:
                    img_data = image_lookup[key]
                    matched_key = key
                    break

            logging.debug(f"DEBUG: Processing Q{qno}_{language} → Matched key: {matched_key}")

            # Map main diagram with exact filename
            if img_data:
                question_text = question.get('question', '')
                if img_data['main_diagrams']:
                    main_image = img_data['main_diagrams'][0]  # Exact filename
                    if '<image>' in question_text:
                        question['question'] = question_text.replace('<image>', '', 1)
                    question['image_placeholder'] = main_image  # Store exact filename

                # Map options with exact filenames
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        available_images = img_data['options'].get(opt_letter, [])

                        logging.debug(f"DEBUG: Q{qno}_{language} Option {opt_letter} → Images: {available_images}")

                        if available_images:
                            exact_filename = available_images[0]  # Use exact filename
                            if opt_text.lower() == '<image>':
                                question[opt_key] = exact_filename  # Store exact filename
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', exact_filename)
                        else:
                            if opt_text.lower() == '<image>':
                                question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')
            else:
                # No match found for this question
                logging.debug(f"DEBUG: No images found for Q{qno}_{language}")
                if '<image>' in question.get('question', ''):
                    question['image_placeholder'] = "IMAGE_MISSING"
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        if opt_text.lower() == '<image>':
                            question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                        elif '<image>' in opt_text:
                            question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')

        return questions

    def detect_bounding_boxes(self, image_path):
        """Enhanced YOLO detection for better accuracy."""
        try:
            model = YOLO(r'src/yolo/best.pt')
            
            results = model.predict(
                source=image_path,
                imgsz=640,
                conf=0.01,  # Even lower confidence for maximum recall
                iou=0.1,    # Very low IoU to detect overlapping boxes
                task='detect',
                verbose=False
            )
            
            # Get all detected boxes regardless of class
            boxes = [tuple(map(int, box.xyxy[0].tolist()))
                    for box in results[0].boxes]
            
            return boxes
        except Exception as e:
            logging.error(f"YOLO detection error: {e}")
            return []

    def extract_questions_with_proper_image_mapping(self, question_pdf, start_page, end_page):
        """Enhanced extraction with integrated LaTeX content in text fields."""
        all_questions = []
        pdf_name = os.path.basename(question_pdf)

        for page_num in range(start_page, end_page + 1):
            print(f"📄 Processing page {page_num} of {end_page}...")
            logging.info(f"\n=== Processing page {page_num} ===")

            # Determine question range for this page
            start_qno, end_qno = self.determine_question_range(page_num)
            logging.info(f"Expected question range: Q{start_qno} to Q{end_qno}")

            # Convert PDF page to image
            try:
                doc = fitz.open(question_pdf)
                page = doc[page_num - 1]
                temp_img = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
                temp_img.close()
                page.get_pixmap(dpi=300).save(temp_img.name)
                doc.close()

                # Detect and classify ONLY visual images (not mathematical content)
                boxes = self.detect_bounding_boxes(temp_img.name)
                logging.info(f"Detected {len(boxes)} bounding boxes")
                
                if boxes:
                    classification_response = self.identify_and_classify_images(temp_img.name, boxes)
                    logging.info(f"Classification response: {classification_response}")
                    
                    classified_images = self.parse_image_classification(classification_response)
                    logging.info(f"Found {len(classified_images)} valid visual elements")
                    
                    # Process and save ONLY true visual images
                    saved_images = self.process_and_save_classified_images(
                        temp_img.name, boxes, classified_images, page_num, pdf_name
                    )
                else:
                    saved_images = []
                
                os.remove(temp_img.name)

            except Exception as e:
                logging.error(f"Error processing page {page_num}: {e}")
                saved_images = []

            # Extract questions with integrated mathematical content as LaTeX in text fields
            try:
                with open(question_pdf, "rb") as q_file:
                    question_data = q_file.read()

                # Enhanced prompt for integrated LaTeX in text fields
                enhanced_prompt = f"""Extract structured questions from page {page_num} for question numbers ranging from Q{start_qno} to Q{end_qno} with INTEGRATED LATEX FORMAT:

    CRITICAL FORMATTING RULES:
    1. Use ONLY double dollar signs ($$) for display math: $$x^2 + y^2 = r^2$$
    2. NEVER use single dollar signs ($) or triple dollar signs ($$$)
    3. All mathematical expressions must be wrapped in $$...$$ delimiters
    4. Mathematical content should be INTEGRATED directly into the text field, not separate
    5. blooms_level MUST be predicted and filled with one of: "Applying", "Remembering", "Understanding", "Analyzing"

    QUESTION COLUMN JSON STRUCTURE:
    {{
        "text": "What will be the result of $$\\frac{{a}}{{b}}$$ when solving this equation?",
        "image": ["array of all image filenames for this question"],
        "tables": {{"table1": [["row1col1", "row1col2"], ["row2col1", "row2col2"]], "table2": []}}
    }}

    OPTION COLUMNS JSON STRUCTURE:
    {{
        "text": "The answer is $$x^{{2}} + y^{{2}} = r^{{2}}$$",
        "img": "image_filename.jpg"
    }}

    INTEGRATED MATHEMATICAL CONTENT EXAMPLES:
    - Questions with math: "Find the value of $$\\int_{{0}}^{{\\pi}} \\sin x \\, dx$$ and explain the result"
    - Options with math: "$$\\frac{{\\pi}}{{2}}$$ is the correct answer"
    - Mixed content: "The equation $$x^{{2}} + y^{{2}} = r^{{2}}$$ represents a circle with radius r"
    - Pure math: "$$\\lim_{{x \\to 0}} \\frac{{\\sin x}}{{x}} = 1$$"
    - Chemical formulas: "The compound $$H_{{2}}SO_{{4}}$$ is sulfuric acid"

    EXTRACTION GUIDELINES:
    1. Each question may appear in BOTH English and Tamil — extract both versions
    2. For mathematical content: Integrate LaTeX directly into the text field using $$...$$ syntax
    3. For visual content: Use <image> placeholder only for actual photos/illustrations
    4. DO NOT create separate latex_content fields - integrate math into text
    5. Preserve Unicode characters for non-mathematical text
    6. blooms_level should be predicted based on question complexity and type
    7. type_of_question should be one of: "MCQ", "Short Answer", "Essay"

    JSON OUTPUT FORMAT:
    [
    {{
        "qno": "1",
        "language": "English", 
        "question": {{"text": "Consider the following figure and find the value of $$\\int_{{0}}^{{\\pi}} \\sin x \\, dx$$", "image": ["<image>"], "tables": {{}}}},
        "optiona": {{"text": "$$\\pi$$", "img": ""}},
        "optionb": {{"text": "$$2$$", "img": ""}}, 
        "optionc": {{"text": "$$\\frac{{\\pi}}{{2}}$$", "img": ""}},
        "optiond": {{"text": "$$0$$", "img": ""}},
        "type_of_question": "MCQ",
        "mark": "1",
        "blooms_level": "Applying"
    }},
    {{
        "qno": "2",
        "language": "English",
        "question": {{"text": "Solve the matrix equation $$\\begin{{pmatrix}} 2 & 1 \\\\ 3 & 4 \\end{{pmatrix}} \\begin{{pmatrix}} x \\\\ y \\end{{pmatrix}} = \\begin{{pmatrix}} 5 \\\\ 6 \\end{{pmatrix}}$$", "image": [], "tables": {{}}}},
        "optiona": {{"text": "$$x = 1, y = 2$$", "img": ""}},
        "optionb": {{"text": "$$x = 2, y = 1$$", "img": ""}},
        "optionc": {{"text": "$$x = 3, y = 0$$", "img": ""}}, 
        "optiond": {{"text": "$$x = 0, y = 3$$", "img": ""}},
        "type_of_question": "MCQ",
        "mark": "1",
        "blooms_level": "Understanding"
    }}
    ]

    CRITICAL: 
    - Use ONLY $$...$$ for all math expressions. No single $ or triple $$$ allowed.
    - blooms_level MUST be predicted and cannot be empty
    - Mathematical content must be INTEGRATED into text fields, not separate
    - All question and option fields must follow the exact JSON structure shown above
    """

                # Configure Gemini for enhanced mathematical processing
                generation_config = {
                    "temperature": 0.05,  # Lower temperature for more precise mathematical extraction
                    "top_p": 0.7,
                    "top_k": 30,
                    "max_output_tokens": 12288,  # Increased for complex mathematical content
                }

                response = self.model.generate_content(
                    [
                        {"mime_type": "application/pdf", "data": question_data},
                        enhanced_prompt
                    ],
                    generation_config=generation_config
                )

                if response and hasattr(response, "text") and response.text.strip():
                    try:
                        # Enhanced JSON cleaning with LaTeX preservation
                        response_text = self.clean_json_response_with_latex(response.text)
                        
                        # Parse JSON with mathematical content support
                        questions = json.loads(response_text, strict=False)
                        
                        # Validate and clean extracted questions while preserving LaTeX
                        questions = self.validate_and_clean_questions_with_structured_format(questions)
                        
                        # Map only visual images to questions (mathematical content already extracted as LaTeX)
                        questions_with_images = self.map_images_to_questions_structured_format(questions, saved_images)
                        
                        all_questions.extend(questions_with_images)
                        logging.info(f"Extracted {len(questions_with_images)} questions from page {page_num}")

                    except json.JSONDecodeError as e:
                        logging.error(f"JSON parsing error on page {page_num}: {e}")
                        logging.error(f"Problematic response: {response.text[:500]}...")

            except Exception as e:
                logging.error(f"Error extracting questions from page {page_num}: {e}")

            time.sleep(2)
        
        return all_questions


    def validate_and_clean_questions_with_structured_format(self, questions):
        """Validate and clean extracted questions with new structured format."""
        cleaned_questions = []
        
        for q in questions:
            if not isinstance(q, dict):
                continue
            
            # Initialize structured question format
            cleaned_q = {
                'qno': str(q.get('qno', '')).strip(),
                'language': str(q.get('language', 'English')).strip(),
                'question': self.structure_question_field(q.get('question', {})),
                'optiona': self.structure_option_field(q.get('optiona', {})),
                'optionb': self.structure_option_field(q.get('optionb', {})),
                'optionc': self.structure_option_field(q.get('optionc', {})),
                'optiond': self.structure_option_field(q.get('optiond', {})),
                'type_of_question': str(q.get('type_of_question', 'MCQ')).strip(),
                'mark': str(q.get('mark', '1')).strip(),
                'blooms_level': self.validate_blooms_level(q.get('blooms_level', ''))
            }
            
            # Skip if no question text
            if not cleaned_q['question'].get('text', '').strip():
                continue
            
            # Clean question number
            cleaned_q['qno'] = re.sub(r'^[Qq]', '', cleaned_q['qno'])
            
            # Validate language
            if cleaned_q['language'].lower() not in ['english', 'tamil']:
                cleaned_q['language'] = 'English'
            else:
                cleaned_q['language'] = cleaned_q['language'].title()
            
            # Validate LaTeX syntax in structured format
            cleaned_q = self.validate_latex_syntax_structured(cleaned_q)
            
            cleaned_questions.append(cleaned_q)
        
        return cleaned_questions

    def structure_question_field(self, question_data):
        """Structure question field without separate latex_content."""
        if isinstance(question_data, str):
            # If it's a string, parse it as legacy format
            return {
                "text": question_data,
                "image": [],
                "tables": {}
            }
        elif isinstance(question_data, dict):
            # Integrate latex_content into text if it exists separately
            text = str(question_data.get('text', ''))
            latex_content = str(question_data.get('latex_content', ''))
            
            # If latex_content exists separately, integrate it into text
            if latex_content and latex_content not in text:
                if text:
                    text = f"{text} {latex_content}"
                else:
                    text = latex_content
            
            return {
                "text": text,
                "image": question_data.get('image', []) if isinstance(question_data.get('image'), list) else [question_data.get('image', '')],
                "tables": question_data.get('tables', {}) if isinstance(question_data.get('tables'), dict) else {}
            }
        else:
            return {
                "text": str(question_data),
                "image": [],
                "tables": {}
            }


    def structure_option_field(self, option_data):
        """Structure option field without separate latex attribute."""
        if isinstance(option_data, str):
            # If it's a string, parse it as legacy format
            return {
                "text": option_data,
                "img": ""
            }
        elif isinstance(option_data, dict):
            # Integrate latex into text if it exists separately
            text = str(option_data.get('text', ''))
            latex = str(option_data.get('latex', ''))
            
            # If latex exists separately, integrate it into text
            if latex and latex not in text:
                if text:
                    text = f"{text} {latex}"
                else:
                    text = latex
            
            return {
                "text": text,
                "img": str(option_data.get('img', ''))
            }
        else:
            return {
                "text": str(option_data),
                "img": ""
            }


    def validate_blooms_level(self, blooms_level):
        """Validate and ensure blooms_level is one of the required values."""
        valid_levels = ["Applying", "Remembering", "Understanding", "Analyzing"]
        
        if blooms_level and blooms_level in valid_levels:
            return blooms_level
        
        # If empty or invalid, predict based on common patterns
        # This is a simple prediction - in real implementation, you might want more sophisticated logic
        return "Understanding"  # Default to Understanding if not specified

    def validate_latex_syntax_structured(self, question):
        """Validate and fix LaTeX syntax in integrated text fields."""
        # Validate LaTeX in question text field
        if 'text' in question['question']:
            question['question']['text'] = self.fix_latex_syntax(question['question']['text'])
        
        # Validate LaTeX in option text fields
        for opt_key in ['optiona', 'optionb', 'optionc', 'optiond']:
            if 'text' in question[opt_key]:
                question[opt_key]['text'] = self.fix_latex_syntax(question[opt_key]['text'])
        
        return question

    def fix_latex_syntax(self, content):
        """Fix common LaTeX syntax issues."""
        if not content:
            return content
        
        # Fix triple dollar signs to double (common error)
        content = re.sub(r'\$\$\$([^$]+)\$\$\$', r'$$\1$$', content)
        
        # Don't modify already properly formatted display math ($$...$$)
        # Only fix standalone single dollars that aren't part of display math
        content = re.sub(r'(?<!\$)\$([^$\n]+)\$(?!\$)', r'$$\1$$', content)
        
        # Fix common LaTeX command formatting
        content = re.sub(r'\\frac\s*\{([^}]+)\}\s*\{([^}]+)\}', r'\\frac{\1}{\2}', content)
        content = re.sub(r'\\sqrt\s*\{([^}]+)\}', r'\\sqrt{\1}', content)
        
        # Fix subscripts and superscripts
        content = re.sub(r'([a-zA-Z])_([a-zA-Z0-9]+)(?![{}])', r'\1_{\2}', content)
        content = re.sub(r'([a-zA-Z])\^([a-zA-Z0-9]+)(?![{}])', r'\1^{\2}', content)
        
        return content

    def map_images_to_questions_structured_format(self, questions, saved_images):
        """Map images to questions in structured format."""
        
        def normalize_language(lang):
            return lang.strip().title()

        def normalize_qno(qno):
            return str(qno).strip().lstrip('Q')

        def get_possible_keys(qno, lang):
            q = normalize_qno(qno)
            l = normalize_language(lang)
            return [f"{q}_{l}", f"Q{q}_{l}", q, f"Q{q}"]

        # Build image lookup table with exact filenames
        image_lookup = {}

        logging.debug(f"DEBUG: Total saved images: {len(saved_images)}")
        for img_info in saved_images:
            try:
                question_num = normalize_qno(img_info['question'])
                language = normalize_language(img_info['language'])
                filename = img_info['filename']
                img_type = img_info['type'].lower()

                possible_keys = get_possible_keys(question_num, language)

                for key in possible_keys:
                    if key not in image_lookup:
                        image_lookup[key] = {
                            'main_diagrams': [],
                            'options': {'A': [], 'B': [], 'C': [], 'D': []}
                        }

                    if img_type == 'main_diagram' or 'diagram' in filename.lower():
                        if filename not in image_lookup[key]['main_diagrams']:
                            image_lookup[key]['main_diagrams'].append(filename)
                    elif img_type.startswith('option_') or 'option' in filename.lower():
                        match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                        if match:
                            option_letter = match.group(1).upper()
                            if filename not in image_lookup[key]['options'][option_letter]:
                                image_lookup[key]['options'][option_letter].append(filename)
            except Exception as e:
                logging.warning(f"WARNING: Skipping image entry due to error: {e} -> {img_info}")

        # Map images to questions in structured format
        for question in questions:
            qno = normalize_qno(question.get('qno', ''))
            language = normalize_language(question.get('language', ''))

            possible_keys = get_possible_keys(qno, language)

            img_data = None
            matched_key = None
            for key in possible_keys:
                if key in image_lookup:
                    img_data = image_lookup[key]
                    matched_key = key
                    break

            logging.debug(f"DEBUG: Processing Q{qno}_{language} → Matched key: {matched_key}")

            # Map main diagram images to question field
            if img_data and img_data['main_diagrams']:
                # Replace <image> placeholders with actual filenames
                question_images = []
                for img in question['question']['image']:
                    if img == '<image>':
                        question_images.extend(img_data['main_diagrams'])
                    else:
                        question_images.append(img)
                question['question']['image'] = question_images
            else:
                # Remove <image> placeholders if no images found
                question['question']['image'] = [img for img in question['question']['image'] if img != '<image>']

            # Map option images
            option_mapping = {'optiona': 'A', 'optionb': 'B', 'optionc': 'C', 'optiond': 'D'}
            
            for opt_key, opt_letter in option_mapping.items():
                if img_data and img_data['options'][opt_letter]:
                    # If option has placeholder, replace with actual filename
                    if question[opt_key]['img'] == '<image>' or not question[opt_key]['img']:
                        question[opt_key]['img'] = img_data['options'][opt_letter][0]
                else:
                    # Clear placeholder if no image found
                    if question[opt_key]['img'] == '<image>':
                        question[opt_key]['img'] = ""

        return questions

    def save_to_excel_with_structured_format(self, questions, excel_path):
        """Save questions to Excel with integrated LaTeX format."""
        try:
            # Define Excel columns for integrated format
            excel_columns = [
                'qno', 'language', 'question', 'optiona', 'optionb', 'optionc', 'optiond',
                'type_of_question', 'mark', 'blooms_level'
            ]
            
            # Convert structured format to JSON strings for Excel storage
            for question in questions:
                for col in excel_columns:
                    if col not in question:
                        if col in ['question', 'optiona', 'optionb', 'optionc', 'optiond']:
                            question[col] = {} if col == 'question' else {"text": "", "img": ""}
                        else:
                            question[col] = ""
                
                # Convert structured fields to JSON strings - question field only has text, image, tables
                if isinstance(question['question'], dict):
                    # Ensure question structure without latex_content
                    cleaned_question = {
                        "text": question['question'].get('text', ''),
                        "image": question['question'].get('image', []),
                        "tables": question['question'].get('tables', {})
                    }
                    question['question'] = json.dumps(cleaned_question, ensure_ascii=False)
                
                # Convert option fields to JSON strings - only text and img
                for opt_key in ['optiona', 'optionb', 'optionc', 'optiond']:
                    if isinstance(question[opt_key], dict):
                        cleaned_option = {
                            "text": question[opt_key].get('text', ''),
                            "img": question[opt_key].get('img', '')
                        }
                        question[opt_key] = json.dumps(cleaned_option, ensure_ascii=False)
            
            # Create DataFrame with correct column order
            df = pd.DataFrame(questions)
            df = df[excel_columns]
            
            # Create workbook manually for better Unicode control
            wb = Workbook()
            ws = wb.active
            ws.title = "Questions_Integrated_LaTeX"
            
            # Add headers
            for col_idx, header in enumerate(excel_columns, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                cell.font = Font(bold=True)
            
            # Add data rows with Unicode preservation
            for row_idx, question in enumerate(questions, 2):
                for col_idx, col_name in enumerate(excel_columns, 1):
                    cell_value = question.get(col_name, "")
                    
                    # Preserve Unicode and JSON characters exactly
                    if isinstance(cell_value, str):
                        ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    else:
                        ws.cell(row=row_idx, column=col_idx, value=str(cell_value))
            
            # Set column widths for better readability
            column_widths = {
                'A': 8,   # qno
                'B': 12,  # language  
                'C': 120, # question (increased for JSON content with integrated LaTeX)
                'D': 100, # optiona (increased for JSON with integrated LaTeX)
                'E': 100, # optionb
                'F': 100, # optionc
                'G': 100, # optiond
                'H': 20,  # type_of_question
                'I': 8,   # mark
                'J': 15   # blooms_level
            }
            
            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width
            
            # Set text wrapping for JSON columns
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            # Save with UTF-8 encoding
            wb.save(excel_path)
            logging.info(f"Excel file saved with integrated LaTeX format: {excel_path}")
            
            return True
            
        except Exception as e:
            logging.error(f"Error saving to Excel with integrated LaTeX format: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_pdf(self, pdf_path, start_page=2, end_page=3):
        """Main processing function with integrated LaTeX format support."""
        try:
            logging.info(f"Starting PDF processing: {pdf_path}")
            logging.info(f"Processing pages {start_page} to {end_page}")
            
            # Clear tracking variables
            self.processed_image_signatures.clear()
            self.question_image_counter.clear()
            self.global_image_signatures.clear()
            
            # Extract questions with integrated LaTeX format
            questions = self.extract_questions_with_proper_image_mapping(pdf_path, start_page, end_page)
            
            if not questions:
                logging.info("No questions extracted from the PDF")
                return False
            
            # Generate Excel path with PDF name
            pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
            clean_pdf_name = re.sub(r'[^a-zA-Z0-9_]', '_', pdf_basename)
            excel_path = os.path.join(self.output_dir, f"{clean_pdf_name}_questions_integrated_latex.xlsx")
            
            # Save to Excel with integrated LaTeX format
            success = self.save_to_excel_with_structured_format(questions, excel_path)
            
            if success:
                # Print summary
                total_questions = len(questions)
                english_questions = len([q for q in questions if q['language'] == 'English'])
                tamil_questions = len([q for q in questions if q['language'] == 'Tamil'])
                
                # Count questions with different types of content
                questions_with_images = 0
                questions_with_latex = 0
                questions_with_tables = 0
                questions_with_option_images = 0
                
                for q in questions:
                    question_data = json.loads(q['question']) if isinstance(q['question'], str) else q['question']
                    if question_data.get('image') and any(img for img in question_data['image']):
                        questions_with_images += 1
                    if '$$' in question_data.get('text', ''):
                        questions_with_latex += 1
                    if question_data.get('tables') and question_data['tables']:
                        questions_with_tables += 1
                    
                    # Check option images and LaTeX
                    for opt in ['optiona', 'optionb', 'optionc', 'optiond']:
                        opt_data = json.loads(q[opt]) if isinstance(q[opt], str) else q[opt]
                        if opt_data.get('img'):
                            questions_with_option_images += 1
                            break
                
                logging.info(f"\n=== PROCESSING COMPLETE ===")
                logging.info(f"Excel file saved: {excel_path}")
                logging.info(f"Total questions: {total_questions}")
                logging.info(f"English questions: {english_questions}")
                logging.info(f"Tamil questions: {tamil_questions}")
                logging.info(f"Questions with main images: {questions_with_images}")
                logging.info(f"Questions with integrated LaTeX content: {questions_with_latex}")
                logging.info(f"Questions with tables: {questions_with_tables}")
                logging.info(f"Questions with option images: {questions_with_option_images}")
                logging.info(f"Total unique images saved: {len(self.global_image_signatures)}")
            
            return success
            
        except Exception as e:
            logging.error(f"Error processing PDF: {e}")
            import traceback
            traceback.print_exc()
            return False
