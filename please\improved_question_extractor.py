import fitz  # PyMuPDF
import pdfplumber
import pymupdf4llm
import pandas as pd
import os
import re
from PIL import Image
import io
import unicodedata

def is_aakash_logo(pil_img):
    """Precisely identify Aakash logos based on exact analysis"""
    width, height = pil_img.size
    
    # Exact Aakash logo sizes identified from analysis
    aakash_logo_sizes = [
        (403, 284),  # Main Aakash logo - found 10 times
        (2, 2),      # Tiny artifacts - found 15 times
        (282, 203),  # Secondary logo variant
        (326, 313),  # Another logo variant
        (400, 319),  # Close variant
    ]
    
    # Check for exact matches
    for logo_width, logo_height in aakash_logo_sizes:
        if width == logo_width and height == logo_height:
            return True
    
    # Additional safety checks for logo-like images
    if width < 100 or height < 100:  # Very small images
        return True
    
    # Very wide or tall images (headers/footers)
    aspect_ratio = width / height
    if aspect_ratio > 5 or aspect_ratio < 0.2:
        return True
    
    return False

def extract_text_multiple_methods(pdf_path, page_num):
    """Extract text using multiple methods and choose the best one"""
    
    methods_results = {}
    
    # Method 1: PyMuPDF (current)
    try:
        doc = fitz.open(pdf_path)
        page = doc[page_num]
        text1 = page.get_text()
        doc.close()
        methods_results['pymupdf'] = text1
    except Exception as e:
        methods_results['pymupdf'] = f"Error: {e}"
    
    # Method 2: pdfplumber
    try:
        with pdfplumber.open(pdf_path) as pdf:
            if page_num < len(pdf.pages):
                page = pdf.pages[page_num]
                text2 = page.extract_text() or ""
                methods_results['pdfplumber'] = text2
    except Exception as e:
        methods_results['pdfplumber'] = f"Error: {e}"
    
    # Method 3: pymupdf4llm (optimized for LLM)
    try:
        doc = fitz.open(pdf_path)
        page = doc[page_num]
        text3 = pymupdf4llm.to_markdown(doc, pages=[page_num])
        doc.close()
        methods_results['pymupdf4llm'] = text3
    except Exception as e:
        methods_results['pymupdf4llm'] = f"Error: {e}"
    
    # Choose the best method based on content quality
    best_text = ""
    best_score = 0
    
    for method, text in methods_results.items():
        if isinstance(text, str) and not text.startswith("Error:"):
            # Score based on question patterns found
            question_patterns = len(re.findall(r'\d+\.', text))
            option_patterns = len(re.findall(r'\(\d+\)', text))
            score = question_patterns + option_patterns
            
            if score > best_score:
                best_score = score
                best_text = text
    
    return best_text, methods_results

def clean_text_advanced(text):
    """Advanced text cleaning with better encoding handling"""
    if not text:
        return ""
    
    # Normalize unicode characters
    text = unicodedata.normalize('NFKD', text)
    
    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())
    
    # Advanced encoding fixes
    replacements = {
        # Common encoding issues
        'â€™': "'", 'â€œ': '"', 'â€': '"', 'â€"': '–', 'â€"': '—',
        # Box characters and symbols
        '□': '', '■': '', '▪': '', '▫': '', '◦': '', '•': '',
        '\u25a0': '', '\u25a1': '', '\u2610': '', '\u2611': '', '\u2612': '',
        '\uf0b7': '', '\uf020': ' ', '\uf0d8': '', '\uf0fc': '',
        # Mathematical symbols that might be misread
        '×': 'x', '÷': '/', '±': '±', '≤': '<=', '≥': '>=',
        # Greek letters commonly used in physics
        'α': 'alpha', 'β': 'beta', 'γ': 'gamma', 'δ': 'delta',
        'θ': 'theta', 'λ': 'lambda', 'μ': 'mu', 'π': 'pi',
        'σ': 'sigma', 'τ': 'tau', 'φ': 'phi', 'ω': 'omega',
    }
    
    for old, new in replacements.items():
        text = text.replace(old, new)
    
    # Remove any remaining non-printable characters except common ones
    text = ''.join(char for char in text if char.isprintable() or char in '\n\t ')
    
    return text

def extract_questions_advanced(text):
    """Advanced question extraction with better pattern matching"""
    questions = []
    
    # Clean the text
    text = clean_text_advanced(text)
    
    # Split into lines
    lines = text.split('\n')
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for question start pattern: number followed by dot
        question_match = re.match(r'^(\d+)\.\s*(.*)$', line)
        if question_match:
            question_num = int(question_match.group(1))
            question_start = question_match.group(2)
            
            # Collect full question text
            question_text = question_start
            i += 1
            
            # Gather question text until we hit options
            while i < len(lines):
                current_line = lines[i].strip()
                
                # Stop if we hit options (1), (2), etc.
                if re.match(r'^\(\d+\)', current_line):
                    break
                
                # Stop if we hit next question number
                if re.match(r'^\d+\.\s', current_line):
                    i -= 1
                    break
                
                # Stop if we hit "Answer" or "Sol."
                if (current_line.startswith('Answer') or 
                    current_line.startswith('Sol.') or
                    current_line.startswith('Solution')):
                    break
                
                # Add to question text if it's substantial
                if current_line and len(current_line) > 3:
                    question_text += " " + current_line
                
                i += 1
            
            # Now collect options
            options = []
            while i < len(lines) and len(options) < 4:
                current_line = lines[i].strip()
                
                # Look for option pattern (1), (2), (3), (4)
                option_match = re.match(r'^\((\d+)\)\s*(.+)', current_line)
                if option_match:
                    option_num = int(option_match.group(1))
                    option_text = option_match.group(2)
                    
                    # Ensure options are in order
                    if option_num == len(options) + 1:
                        options.append(option_text)
                
                # Stop conditions
                if (current_line.startswith('Answer') or 
                    re.match(r'^\d+\.\s', current_line) or
                    current_line.startswith('Sol.') or
                    current_line.startswith('Solution')):
                    break
                
                i += 1
            
            # Only add question if we have good content
            if (question_text.strip() and len(options) >= 2 and 
                len(question_text) > 10):  # Minimum question length
                questions.append({
                    'Question_Number': question_num,
                    'Question_Text': question_text.strip(),
                    'Option_1': options[0] if len(options) > 0 else '',
                    'Option_2': options[1] if len(options) > 1 else '',
                    'Option_3': options[2] if len(options) > 2 else '',
                    'Option_4': options[3] if len(options) > 3 else '',
                })
                print(f"    ✅ Q{question_num}: {question_text[:60]}...")
        
        i += 1
    
    return questions

def extract_first_20_questions_improved():
    """Extract first 20 questions with improved methods"""
    
    pdf_path = "akash.pdf"
    output_dir = "output_improved"
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    images_dir = os.path.join(output_dir, 'images')
    os.makedirs(images_dir, exist_ok=True)
    
    # Open PDF
    doc = fitz.open(pdf_path)
    
    all_questions = []
    all_images = []
    global_image_counter = 0
    
    print(f"🚀 IMPROVED EXTRACTION: First 20 questions")
    print(f"📄 PDF: {len(doc)} pages")
    print(f"🎯 Target: 20 questions with precise logo filtering")
    
    # Process pages until we get 20 questions
    for page_num in range(len(doc)):
        if len(all_questions) >= 20:
            print(f"✅ Reached 20 questions, stopping")
            break
            
        print(f"\n📄 Processing page {page_num + 1}...")
        
        # Extract text using multiple methods
        best_text, methods = extract_text_multiple_methods(pdf_path, page_num)
        
        # Extract images with precise logo filtering
        page = doc[page_num]
        image_list = page.get_images()
        
        for img_index, img in enumerate(image_list):
            try:
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                    pil_img = Image.open(io.BytesIO(img_data))
                    
                    width, height = pil_img.size
                    
                    # Precise Aakash logo filtering
                    if is_aakash_logo(pil_img):
                        print(f"    🚫 Skipped logo: {width}x{height}")
                        pix = None
                        continue
                    
                    # Save content image
                    img_filename = f"question_img_{global_image_counter}.png"
                    img_path = os.path.join(images_dir, img_filename)
                    pil_img.save(img_path)
                    all_images.append(f"images/{img_filename}")
                    global_image_counter += 1
                    print(f"    💾 Saved: {img_filename} ({width}x{height})")
                
                pix = None
                
            except Exception as e:
                continue
        
        # Extract questions using advanced method
        page_questions = extract_questions_advanced(best_text)
        all_questions.extend(page_questions)
        
        print(f"    📝 Extracted: {len(page_questions)} questions")
        print(f"    📊 Total so far: {len(all_questions)} questions")
    
    doc.close()
    
    # Take only first 20 questions
    final_questions = all_questions[:20]
    
    # Assign images to questions
    for i, question in enumerate(final_questions):
        if i < len(all_images):
            question['Question_Image_Path'] = all_images[i]
        else:
            question['Question_Image_Path'] = ''
        
        # Add empty columns
        question['Option_1_Image_Path'] = ''
        question['Option_2_Image_Path'] = ''
        question['Option_3_Image_Path'] = ''
        question['Option_4_Image_Path'] = ''
        question['Table_JSON_Path'] = ''
    
    # Save to CSV
    df = pd.DataFrame(final_questions)
    csv_path = os.path.join(output_dir, 'questions.csv')
    df.to_csv(csv_path, index=False)
    
    print(f"\n🎉 === IMPROVED EXTRACTION COMPLETE ===")
    print(f"✅ Questions extracted: {len(final_questions)}")
    print(f"✅ Images saved: {global_image_counter}")
    print(f"✅ Aakash logos filtered: 403x284 and variants")
    print(f"✅ Advanced text cleaning applied")
    print(f"✅ Multiple extraction methods used")
    print(f"📁 CSV: {csv_path}")
    print(f"📁 Images: {images_dir}")
    
    # Show sample
    print(f"\n📋 === SAMPLE QUESTIONS ===")
    for i, q in enumerate(final_questions[:3]):
        print(f"\nQ{q['Question_Number']}: {q['Question_Text'][:80]}...")
        print(f"  (1) {q['Option_1'][:40]}...")
        print(f"  (2) {q['Option_2'][:40]}...")
        print(f"  🖼️  {q['Question_Image_Path']}")
    
    return csv_path

if __name__ == "__main__":
    extract_first_20_questions_improved()
