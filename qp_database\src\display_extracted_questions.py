import pandas as pd
from pathlib import Path

def display_questions(excel_path):
    df = pd.read_excel(excel_path)
    for idx, row in df.iterrows():
        print(f"Q{idx+1}: {row['question']}")
        print(f"  A) {row['optiona']}")
        print(f"  B) {row['optionb']}")
        print(f"  C) {row['optionc']}")
        print(f"  D) {row['optiond']}")
        print('-' * 60)

def main():
    excel_path = Path("../data/output/excel/clgdunia_questions_simple.xlsx")
    display_questions(excel_path)

if __name__ == "__main__":
    main() 