"""
Modern AI Document Understanding Pipeline
Open Source Implementation for NEET/JEE Question Extraction

This package provides a modern alternative to traditional OCR+YOLO approaches
using state-of-the-art AI models for document understanding.
"""

__version__ = "1.0.0"
__author__ = "Modern AI Pipeline"

from .main_pipeline import ModernAIPipeline
from .config import PipelineConfig

__all__ = ["ModernAIPipeline", "PipelineConfig"]
