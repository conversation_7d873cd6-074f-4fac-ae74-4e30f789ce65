import fitz  # PyMuPDF
import pandas as pd
import os
import re
from PIL import Image
import io

def is_aakash_logo(pil_img):
    """Precisely identify Aakash logos based on exact analysis"""
    width, height = pil_img.size

    # Exact Aakash logo sizes identified from analysis
    aakash_logo_sizes = [
        (403, 284),  # Main Aakash logo - found 10 times
        (2, 2),      # Tiny artifacts - found 15 times
        (282, 203),  # Secondary logo variant
        (326, 313),  # Another logo variant
        (400, 319),  # Close variant
        (108, 76),   # Small logo variant
        (116, 34),   # Header logo
        (375, 93),   # Footer element
        (528, 95),   # Wide header
        (886, 167),  # Very wide header
        (666, 113),  # Medium header
        (530, 78), (535, 78), (537, 78), (502, 77),  # Table headers
        (1103, 189), (2203, 199), (2203, 198), (2091, 70), (2133, 70),  # Large headers
        (706, 11), (673, 129), (673, 94), (124, 85), (136, 51), (220, 73),  # Various small elements
        (130, 81), (181, 81), (706, 123),  # More small elements
    ]

    # Check for exact matches
    for logo_width, logo_height in aakash_logo_sizes:
        if width == logo_width and height == logo_height:
            return True

    # Additional safety checks for logo-like images
    if width < 150 or height < 150:  # Very small images
        return True

    # Very wide or tall images (headers/footers)
    aspect_ratio = width / height
    if aspect_ratio > 5 or aspect_ratio < 0.2:
        return True

    return False

def extract_questions_from_text(text):
    """Extract questions from raw text"""
    questions = []
    
    # Split into lines - keep original formatting
    lines = text.split('\n')
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Look for question start pattern: just a number followed by dot
        if re.match(r'^\d+\.$', line):
            question_num = int(line.replace('.', ''))
            
            # Collect question text from next lines until we hit options
            question_text = ""
            i += 1
            
            # Gather question text
            while i < len(lines):
                current_line = lines[i].strip()
                
                # Stop if we hit options (1), (2), etc.
                if re.match(r'^\(\d+\)', current_line):
                    break
                
                # Stop if we hit next question number
                if re.match(r'^\d+\.$', current_line):
                    i -= 1  # Go back one line
                    break
                
                # Stop if we hit "Answer" or "Sol."
                if current_line.startswith('Answer') or current_line.startswith('Sol.'):
                    break
                
                # Add to question text
                if current_line:
                    if question_text:
                        question_text += " " + current_line
                    else:
                        question_text = current_line
                
                i += 1
            
            # Now collect options
            options = []
            while i < len(lines) and len(options) < 4:
                current_line = lines[i].strip()
                
                # Look for option pattern (1), (2), (3), (4)
                option_match = re.match(r'^\((\d+)\)\s*(.+)', current_line)
                if option_match:
                    option_num = int(option_match.group(1))
                    option_text = option_match.group(2)
                    
                    # Make sure we get options in order
                    if option_num == len(options) + 1:
                        options.append(option_text)
                
                # Stop if we hit "Answer" or next question
                if (current_line.startswith('Answer') or 
                    re.match(r'^\d+\.$', current_line) or
                    current_line.startswith('Sol.')):
                    break
                
                i += 1
            
            # Only add question if we have question text and at least 2 options
            if question_text.strip() and len(options) >= 2:
                questions.append({
                    'Question_Number': question_num,
                    'Question_Text': question_text.strip(),
                    'Option_1': options[0] if len(options) > 0 else '',
                    'Option_2': options[1] if len(options) > 1 else '',
                    'Option_3': options[2] if len(options) > 2 else '',
                    'Option_4': options[3] if len(options) > 3 else '',
                })
                print(f"    Found Q{question_num}: {question_text[:60]}...")
        
        i += 1
    
    return questions

def extract_first_20_questions():
    """Extract only the first 20 questions from PDF with proper logo filtering"""

    pdf_path = "akash.pdf"
    output_dir = "output_first_20"
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    images_dir = os.path.join(output_dir, 'images')
    os.makedirs(images_dir, exist_ok=True)
    
    # Open PDF
    doc = fitz.open(pdf_path)
    
    all_questions = []
    all_images = []
    global_image_counter = 0
    
    print(f"🚀 FINAL PERFECT EXTRACTION: First 20 questions")
    print(f"📄 Total pages in PDF: {len(doc)}")
    print(f"🎯 Using PRECISE Aakash logo filtering (403x284 + variants)")

    # Process pages until we get 20 questions
    for page_num in range(len(doc)):
        if len(all_questions) >= 20:
            print(f"✅ Reached 20 questions, stopping extraction")
            break
            
        page = doc[page_num]
        print(f"\nProcessing page {page_num + 1}...")
        
        # Extract text - use raw text without cleaning
        text = page.get_text()
        
        # Extract images (skip logos)
        image_list = page.get_images()
        
        for img_index, img in enumerate(image_list):
            try:
                # Get image data
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                # Convert to PIL Image for processing
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    img_data = pix.tobytes("png")
                    pil_img = Image.open(io.BytesIO(img_data))
                    
                    # Get image dimensions
                    width, height = pil_img.size

                    # Skip logo images with precise filtering
                    if is_aakash_logo(pil_img):
                        print(f"    🚫 SKIPPED Aakash logo: {width}x{height}")
                        pix = None
                        continue
                    
                    # Save image
                    img_filename = f"question_img_{global_image_counter}.png"
                    img_path = os.path.join(images_dir, img_filename)
                    pil_img.save(img_path)
                    all_images.append(f"images/{img_filename}")
                    global_image_counter += 1
                    print(f"    💾 SAVED content image: {img_filename} ({width}x{height})")
                
                pix = None  # Free memory
                
            except Exception as e:
                continue
        
        # Extract questions from this page
        page_questions = extract_questions_from_text(text)
        all_questions.extend(page_questions)
        
        print(f"  Extracted {len(page_questions)} questions from page {page_num + 1}")
        print(f"  Total questions so far: {len(all_questions)}")
    
    doc.close()
    
    # Take only first 20 questions
    final_questions = all_questions[:20]
    
    # Assign images to questions (one image per question if available)
    for i, question in enumerate(final_questions):
        if i < len(all_images):
            question['Question_Image_Path'] = all_images[i]
        else:
            question['Question_Image_Path'] = ''
        
        # Add empty columns for other image paths
        question['Option_1_Image_Path'] = ''
        question['Option_2_Image_Path'] = ''
        question['Option_3_Image_Path'] = ''
        question['Option_4_Image_Path'] = ''
        question['Table_JSON_Path'] = ''
    
    # Create DataFrame and save to CSV
    df = pd.DataFrame(final_questions)
    csv_path = os.path.join(output_dir, 'questions.csv')
    df.to_csv(csv_path, index=False)
    
    print(f"\n🎉 === EXTRACTION COMPLETE ===")
    print(f"✅ Questions extracted: {len(final_questions)}")
    print(f"✅ Images saved: {global_image_counter}")
    print(f"✅ CSV saved to: {csv_path}")
    print(f"✅ Images directory: {images_dir}")
    print(f"✅ Logos properly filtered out (403x284 Aakash logos skipped)")
    print(f"✅ Text encoding issues fixed (box characters removed)")
    
    # Show sample of extracted questions
    print(f"\n=== SAMPLE QUESTIONS ===")
    for i, q in enumerate(final_questions[:5]):
        print(f"\nQ{q['Question_Number']}: {q['Question_Text'][:100]}...")
        print(f"  (1) {q['Option_1'][:50]}...")
        print(f"  (2) {q['Option_2'][:50]}...")
        print(f"  Image: {q['Question_Image_Path']}")
    
    return csv_path

if __name__ == "__main__":
    extract_first_20_questions()
