{% extends "base.html" %}

{% block title %}Question {{ question.qno }} - Question Viewer{% endblock %}

{% block content %}
<div class="header">
    <h1>📝 Question {{ question.qno }}</h1>
    <p>Individual Question View</p>
</div>

<div class="navigation">
    <a href="/" class="nav-button">🏠 All Questions</a>
    {% if question.qno > 1 %}
        <a href="/question/{{ question.qno - 1 }}" class="nav-button">⬅️ Previous</a>
    {% endif %}
    {% if question.qno < 50 %}
        <a href="/question/{{ question.qno + 1 }}" class="nav-button">➡️ Next</a>
    {% endif %}
</div>

<div class="question-card" style="margin-top: 30px;">
    <div class="question-header">
        <div class="question-number">Q{{ question.qno }}</div>
        <div class="question-meta">
            <span class="language-tag">{{ question.language }}</span>
            {% if question.has_image %}
                <span class="image-indicator">📷 Has Image</span>
            {% endif %}
        </div>
    </div>
    
    <div class="question-text" style="font-size: 1.2em; line-height: 1.8;">
        {% if question.image_files %}
            <div style="text-align: center; margin: 20px 0;">
                {% for image_file in question.image_files %}
                    <img src="/images/{{ image_file }}" alt="Question Image" class="question-image" style="max-width: 80%; margin: 10px;">
                {% endfor %}
            </div>
        {% endif %}
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 5px solid #667eea;">
            {{ question.question|replace(question.image_files[0] if question.image_files else '', '')|safe }}
        </div>
    </div>
    
    <div style="margin: 30px 0;">
        <h3 style="color: #667eea; margin-bottom: 20px;">📋 Answer Options:</h3>
        <div class="options" style="gap: 20px;">
            <div class="option" style="padding: 20px; font-size: 1.1em;">
                <div class="option-label" style="font-size: 1.2em; margin-bottom: 10px;">A)</div>
                <div>{{ question.optiona }}</div>
            </div>
            <div class="option" style="padding: 20px; font-size: 1.1em;">
                <div class="option-label" style="font-size: 1.2em; margin-bottom: 10px;">B)</div>
                <div>{{ question.optionb }}</div>
            </div>
            <div class="option" style="padding: 20px; font-size: 1.1em;">
                <div class="option-label" style="font-size: 1.2em; margin-bottom: 10px;">C)</div>
                <div>{{ question.optionc }}</div>
            </div>
            <div class="option" style="padding: 20px; font-size: 1.1em;">
                <div class="option-label" style="font-size: 1.2em; margin-bottom: 10px;">D)</div>
                <div>{{ question.optiond }}</div>
            </div>
        </div>
    </div>
</div>

<div class="navigation" style="margin-top: 40px;">
    <a href="/" class="nav-button">🏠 All Questions</a>
    {% if question.qno > 1 %}
        <a href="/question/{{ question.qno - 1 }}" class="nav-button">⬅️ Previous Question</a>
    {% endif %}
    {% if question.qno < 50 %}
        <a href="/question/{{ question.qno + 1 }}" class="nav-button">➡️ Next Question</a>
    {% endif %}
</div>

<div style="background: white; padding: 20px; border-radius: 10px; margin-top: 30px; text-align: center; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
    <h4 style="color: #667eea; margin-bottom: 15px;">🔧 Question Details</h4>
    <div style="display: flex; justify-content: space-around; flex-wrap: wrap; gap: 20px;">
        <div>
            <strong>Question Number:</strong> {{ question.qno }}
        </div>
        <div>
            <strong>Language:</strong> {{ question.language }}
        </div>
        <div>
            <strong>Has Images:</strong> {{ "Yes" if question.has_image else "No" }}
        </div>
        {% if question.image_files %}
        <div>
            <strong>Image Files:</strong> {{ question.image_files|length }}
        </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        const currentQno = {{ question.qno }};
        
        if (e.key === 'ArrowLeft' && currentQno > 1) {
            window.location.href = `/question/${currentQno - 1}`;
        } else if (e.key === 'ArrowRight' && currentQno < 50) {
            window.location.href = `/question/${currentQno + 1}`;
        } else if (e.key === 'Escape' || e.key === 'Home') {
            window.location.href = '/';
        }
    });
    
    // Add image zoom functionality
    const images = document.querySelectorAll('.question-image');
    images.forEach(img => {
        img.style.cursor = 'pointer';
        img.addEventListener('click', function() {
            if (this.style.transform === 'scale(1.5)') {
                this.style.transform = 'scale(1)';
                this.style.zIndex = '1';
            } else {
                this.style.transform = 'scale(1.5)';
                this.style.zIndex = '1000';
                this.style.position = 'relative';
            }
        });
    });
});
</script>

<div style="position: fixed; bottom: 20px; right: 20px; background: #667eea; color: white; padding: 10px; border-radius: 5px; font-size: 0.9em; opacity: 0.8;">
    💡 Use ← → arrow keys to navigate, ESC to go home, click images to zoom
</div>
{% endblock %}
