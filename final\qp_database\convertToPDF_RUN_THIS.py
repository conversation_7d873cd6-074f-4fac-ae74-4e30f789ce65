import os
import pandas as pd
import json
import re
import random
from reportlab.lib.pagesizes import letter
from reportlab.lib.colors import HexColor, black, lightblue, beige, grey, red
from reportlab.platypus import (
    SimpleDocTemplate, Paragraph, Spacer, 
    PageBreak, Table, TableStyle, Image as RLImage
)
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfutils
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
import logging
from pathlib import Path
from reportlab.platypus import KeepTogether

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def register_fonts():
    """Register Tamil and Unicode fonts with proper error handling"""
    try:
        # Try to register Noto Sans Tamil for Tamil text
        if os.path.exists('NotoSansTamil-Regular.ttf'):
            pdfmetrics.registerFont(TTFont('NotoSansTamil', 'NotoSansTamil-Regular.ttf'))
            if os.path.exists('NotoSansTamil-Bold.ttf'):
                pdfmetrics.registerFont(TTFont('NotoSansTamil-Bold', 'NotoSansTamil-Bold.ttf'))
            logger.info("Noto Sans Tamil fonts registered successfully")
            return 'NotoSansTamil'
    except Exception as e:
        logger.warning(f"Failed to register Noto Sans Tamil: {e}")
    
    try:
        # Fallback to DejaVu fonts
        if os.path.exists('DejaVuSans.ttf'):
            pdfmetrics.registerFont(TTFont('DejaVuSans', 'DejaVuSans.ttf'))
            if os.path.exists('DejaVuSans-Bold.ttf'):
                pdfmetrics.registerFont(TTFont('DejaVuSans-Bold', 'DejaVuSans-Bold.ttf'))
            logger.info("DejaVu fonts registered successfully")
            return 'DejaVuSans'
    except Exception as e:
        logger.warning(f"Failed to register DejaVu fonts: {e}")
    
    logger.info("Using default Helvetica fonts")
    return 'Helvetica'

def draw_watermark(canvas, doc):
    """Add LYSA Solutions watermark to every page"""
    try:
        canvas.saveState()
        canvas.setFont("Helvetica-Bold", 50)
        canvas.setFillColor(HexColor("#d3d3d3"), alpha=0.3)
        text = "LYSA Solutions"
        text_width = canvas.stringWidth(text, "Helvetica-Bold", 50)
        canvas.translate(doc.width/2, doc.height/2)
        canvas.rotate(45)
        canvas.drawString(-text_width/2, 0, text)
        canvas.restoreState()
    except Exception as e:
        logger.error(f"Error drawing watermark: {e}")

def parse_json_field(field_value):
    """Parse JSON field and extract text, images, and tables"""
    if not field_value or pd.isna(field_value) or str(field_value).strip() == '':
        return {"text": "", "image": [], "img": "", "tables": {}}
    
    try:
        # Convert to string and clean up
        field_str = str(field_value).strip()
        
        # Try to parse as JSON
        if field_str.startswith('{') and field_str.endswith('}'):
            parsed = json.loads(field_str)
            
            # Ensure all required keys exist
            result = {
                "text": parsed.get("text", ""),
                "image": parsed.get("image", []),
                "img": parsed.get("img", ""),
                "tables": parsed.get("tables", {})
            }
            return result
        else:
            # If not JSON, treat as plain text
            return {"text": field_str, "image": [], "img": "", "tables": {}}
            
    except json.JSONDecodeError as e:
        logger.warning(f"Failed to parse JSON field: {e}")
        # Return as plain text if JSON parsing fails
        return {"text": str(field_value), "image": [], "img": "", "tables": {}}
    except Exception as e:
        logger.error(f"Error parsing field: {e}")
        return {"text": str(field_value), "image": [], "img": "", "tables": {}}

def is_mcq_question(question_type):
    """Check if question type is MCQ"""
    if not question_type or pd.isna(question_type):
        return False
    
    question_type_str = str(question_type).strip().lower()
    mcq_keywords = ['mcq', 'multiple choice', 'choice', 'select', 'choose']
    
    return any(keyword in question_type_str for keyword in mcq_keywords)

def has_valid_options(row):
    """Check if row has valid options for MCQ"""
    option_cols = ['optiona', 'optionb', 'optionc', 'optiond']
    valid_options = 0
    
    for opt_col in option_cols:
        if opt_col in row and pd.notna(row[opt_col]):
            option_data = str(row[opt_col]).strip()
            if option_data and option_data not in ['', 'nan', 'None']:
                # Try to parse as JSON to check if it has content
                try:
                    parsed = parse_json_field(option_data)
                    if parsed.get("text", "").strip() or parsed.get("img", "").strip():
                        valid_options += 1
                except:
                    # If not JSON, check if it's plain text
                    if len(option_data) > 1:
                        valid_options += 1
    
    return valid_options >= 2  # At least 2 valid options for MCQ

def find_image_file(image_name, image_repo):
    """Find image file with flexible matching"""
    try:
        if not image_name or not image_repo or not os.path.exists(image_repo):
            return None
            
        # Clean the image name
        clean_name = str(image_name).strip()
        if not clean_name:
            return None
        
        logger.info(f"Looking for image: {clean_name}")
        
        # Get all image files in repository
        image_files = []
        for file in os.listdir(image_repo):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp')):
                image_files.append(file)
        
        # 1. Exact match (case-insensitive)
        for file in image_files:
            if file.lower() == clean_name.lower():
                full_path = os.path.join(image_repo, file)
                logger.info(f"Found exact match: {file}")
                return full_path
        
        # 2. Partial match without extension
        base_name = os.path.splitext(clean_name)[0].lower()
        for file in image_files:
            file_base = os.path.splitext(file)[0].lower()
            if file_base == base_name:
                full_path = os.path.join(image_repo, file)
                logger.info(f"Found partial match: {file}")
                return full_path
        
        # 3. Contains match
        for file in image_files:
            if base_name in file.lower():
                full_path = os.path.join(image_repo, file)
                logger.info(f"Found contains match: {file}")
                return full_path
        
        logger.warning(f"Image not found: {clean_name}")
        return None
        
    except Exception as e:
        logger.error(f"Error finding image {image_name}: {e}")
        return None

def add_image_to_pdf(elements, img_path, max_width=5*inch, max_height=4*inch, is_option=False):
    """Add image to PDF with proper scaling and error handling"""
    try:
        if not img_path or not os.path.exists(img_path):
            if img_path:
                error_msg = f"[Image not found: {os.path.basename(img_path)}]"
                logger.warning(error_msg)
                elements.append(Paragraph(error_msg, ParagraphStyle('Error', fontSize=9, textColor=red)))
            return
        
        # Check file size
        file_size = os.path.getsize(img_path)
        if file_size == 0:
            error_msg = f"[Empty image file: {os.path.basename(img_path)}]"
            logger.warning(error_msg)
            elements.append(Paragraph(error_msg, ParagraphStyle('Error', fontSize=9, textColor=red)))
            return
        
        # Create ReportLab image
        try:
            img = RLImage(img_path)
            img_width, img_height = img.drawWidth, img.drawHeight
            logger.info(f"Loaded image: {os.path.basename(img_path)} ({img_width}x{img_height})")
        except Exception as img_error:
            error_msg = f"[Image load error: {os.path.basename(img_path)}]"
            logger.error(f"ReportLab image error: {img_error}")
            elements.append(Paragraph(error_msg, ParagraphStyle('Error', fontSize=9, textColor=red)))
            return
        
        # Set size limits for options vs questions
        if is_option:
            max_width = 2.5*inch
            max_height = 2*inch
        
        # Calculate scaling factor
        width_ratio = max_width / img_width if img_width > 0 else 1
        height_ratio = max_height / img_height if img_height > 0 else 1
        scale_factor = min(width_ratio, height_ratio, 1)  # Don't scale up
        
        # Create scaled image
        final_width = img_width * scale_factor
        final_height = img_height * scale_factor
        
        scaled_img = RLImage(img_path, width=final_width, height=final_height)
        scaled_img.hAlign = 'CENTER'
        
        # Add image with proper spacing
        elements.append(Spacer(1, 0.4*inch))
        elements.append(scaled_img)
        elements.append(Spacer(1, 0.2*inch))

        logger.info(f"Successfully added image: {os.path.basename(img_path)} (scaled to {final_width:.1f}x{final_height:.1f})")
        
    except Exception as e:
        error_msg = f"[Image processing error: {os.path.basename(img_path) if img_path else 'Unknown'}]"
        logger.error(f"Error adding image: {e}")
        elements.append(Paragraph(error_msg, ParagraphStyle('Error', fontSize=9, textColor=red)))

def process_latex_formulas(text):
    """Enhanced LaTeX formula conversion"""
    if not isinstance(text, str):
        return str(text) if text is not None else ""
    
    # Handle display math mode and inline math mode
    text = re.sub(r'\$\$([^$]+)\$\$', r'\1', text)
    text = re.sub(r'\$([^$]+)\$', r'\1', text)
    
    # Handle fractions
    text = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', r'(\1)/(\2)', text)
    
    # Handle roots
    text = re.sub(r'\\sqrt\{([^}]+)\}', r'√(\1)', text)
    
    # Handle superscripts and subscripts
    text = re.sub(r'\^(\d+)', r'<sup>\1</sup>', text)
    text = re.sub(r'_(\d+)', r'<sub>\1</sub>', text)
    text = re.sub(r'\^\{([^}]+)\}', r'<sup>\1</sup>', text)
    text = re.sub(r'_\{([^}]+)\}', r'<sub>\1</sub>', text)
    
    # Greek letters
    greek_letters = {
        r'\\alpha': 'α', r'\\beta': 'β', r'\\gamma': 'γ', r'\\delta': 'δ',
        r'\\epsilon': 'ε', r'\\theta': 'θ', r'\\pi': 'π', r'\\sigma': 'σ'
    }
    
    for latex_symbol, unicode_symbol in greek_letters.items():
        text = re.sub(latex_symbol, unicode_symbol, text)
    
    # Mathematical operators
    math_symbols = {
        r'\\sum': '∑', r'\\prod': '∏', r'\\int': '∫', r'\\infty': '∞',
        r'\\leq': '≤', r'\\geq': '≥', r'\\neq': '≠', r'\\times': '×'
    }
    
    for latex_symbol, unicode_symbol in math_symbols.items():
        text = re.sub(latex_symbol, unicode_symbol, text)
    
    return text

def validate_and_fix_html(text):
    """Comprehensive HTML validation and fixing for ReportLab compatibility"""
    if not isinstance(text, str):
        return str(text) if text is not None else ""
    
    # Allowed tags in ReportLab
    allowed_tags = {'b', 'i', 'u', 's', 'sub', 'sup', 'br'}
    
    # Simple approach: remove all HTML tags except allowed ones
    import re
    
    # Remove all tags except allowed ones
    def replace_tag(match):
        tag = match.group(1).lower()
        if tag.startswith('/'):
            tag_name = tag[1:]
        else:
            tag_name = tag.split()[0]  # Handle tags with attributes
        
        if tag_name in allowed_tags:
            return f'<{tag}>'
        else:
            return ''  # Remove disallowed tags
    
    text = re.sub(r'<([^>]+)>', replace_tag, text)
    
    return text

def process_tables(tables_data, styles, base_font, bold_font):
    """Process table data from JSON format"""
    try:
        if not tables_data or not isinstance(tables_data, dict):
            return []
        
        table_elements = []
        
        for table_name, table_content in tables_data.items():
            if not table_content:
                continue
                
            # Add table title
            if table_name and table_name != "table":
                title_style = ParagraphStyle(
                    'TableTitle',
                    parent=styles['Normal'],
                    fontSize=11,
                    fontName=bold_font,
                    spaceBefore=10,
                    spaceAfter=5
                )
                table_elements.append(Paragraph(f"<b>{table_name}</b>", title_style))
            
            # Process table content
            if isinstance(table_content, list):
                processed_table = []
                for row in table_content:
                    if isinstance(row, list):
                        processed_row = []
                        for cell in row:
                            cell_text = process_latex_formulas(str(cell))
                            cell_text = validate_and_fix_html(cell_text)
                            processed_row.append(Paragraph(cell_text, styles['Normal']))
                        processed_table.append(processed_row)
                
                if processed_table:
                    tbl = Table(processed_table)
                    tbl.setStyle(TableStyle([
                        ('BACKGROUND', (0,0), (-1,0), lightblue),
                        ('TEXTCOLOR', (0,0), (-1,0), black),
                        ('ALIGN', (0,0), (-1,-1), 'LEFT'),
                        ('VALIGN', (0,0), (-1,-1), 'TOP'),
                        ('FONTNAME', (0,0), (-1,0), bold_font),
                        ('FONTSIZE', (0,0), (-1,0), 10),
                        ('BOTTOMPADDING', (0,0), (-1,0), 12),
                        ('BACKGROUND', (0,1), (-1,-1), beige),
                        ('GRID', (0,0), (-1,-1), 1, black),
                    ]))
                    table_elements.extend([
                        Spacer(1, 0.2*inch),
                        tbl,
                        Spacer(1, 0.2*inch)
                    ])
        
        return table_elements
        
    except Exception as e:
        logger.error(f"Error processing tables: {e}")
        return []

def process_question_content(elements, question_data, image_repo, qno, font_name, styles):
    """Process question content from JSON format"""
    try:
        # Parse question JSON
        parsed_question = parse_json_field(question_data)
        
        # Add question text
        question_text = parsed_question.get("text", "")
        if question_text:
            processed_text = process_latex_formulas(question_text)
            processed_text = validate_and_fix_html(processed_text)
            
            question_style = ParagraphStyle(
                'Question',
                parent=styles['Normal'],
                fontSize=12,
                leftIndent=20,
                leading=16,
                spaceBefore=12,
                spaceAfter=8,
                fontName=font_name,
                alignment=TA_JUSTIFY
            )
            
            q_text = f"<b>Q{qno}.</b> {processed_text}"
            elements.append(Paragraph(q_text, question_style))
        
        # Add question images
        question_images = parsed_question.get("image", [])
        if question_images and isinstance(question_images, list):
            for img_name in question_images:
                if img_name:
                    img_path = find_image_file(img_name, image_repo)
                    add_image_to_pdf(elements, img_path, max_width=5*inch, max_height=4*inch)
        
        # Add tables
        tables_data = parsed_question.get("tables", {})
        if tables_data:
            table_elements = process_tables(tables_data, styles, font_name, f"{font_name}-Bold")
            elements.extend(table_elements)
            
    except Exception as e:
        logger.error(f"Error processing question {qno}: {e}")
        elements.append(Paragraph(f"[Error processing question {qno}]", 
                                ParagraphStyle('Error', fontSize=9, textColor=red)))

def process_option_content(elements, option_data, option_letter, image_repo, styles):
    """Process option content from JSON format"""
    try:
        # Parse option JSON
        parsed_option = parse_json_field(option_data)
        
        # Get option text and image
        option_text = parsed_option.get("text", "")
        option_img = parsed_option.get("img", "")
        
        if not option_text and not option_img:
            return
        
        # Add option text
        if option_text:
            processed_text = process_latex_formulas(option_text)
            processed_text = validate_and_fix_html(processed_text)
            
            option_text_final = f"<b>({option_letter})</b> {processed_text}"
            elements.append(Paragraph(option_text_final, styles['Option']))
        else:
            # Just add the option letter if no text
            elements.append(Paragraph(f"<b>({option_letter})</b>", styles['Option']))
        
        # Add option image
        if option_img:
            img_path = find_image_file(option_img, image_repo)
            add_image_to_pdf(elements, img_path, max_width=2.5*inch, max_height=2*inch, is_option=True)
            
    except Exception as e:
        logger.error(f"Error processing option {option_letter}: {e}")
        elements.append(Paragraph(f"[Error processing option {option_letter}]", 
                                ParagraphStyle('Error', fontSize=9, textColor=red)))

def create_pdf_from_excel(excel_file, output_pdf, language_filter=None, image_repo=None):
    """Create PDF from Excel with JSON-formatted data - Optimized Layout Version"""
    
 
    try:
        if not os.path.exists(excel_file):
            logger.error(f"Excel file does not exist: {excel_file}")
            return False
 
        try:
            df = pd.read_excel(excel_file)
            logger.info(f"Successfully loaded Excel file with {len(df)} rows")
        except Exception as e:
            logger.error(f"Error reading Excel file: {e}")
            return False
 
        if df.empty:
            logger.warning("Excel file is empty")
            return False
 
        if language_filter and 'language' in df.columns:
            original_count = len(df)
            df = df[df['language'].str.lower() == language_filter.lower()]
            logger.info(f"Filtered from {original_count} to {len(df)} questions for language: {language_filter}")
 
        if df.empty:
            logger.warning(f"No questions found for language: {language_filter}")
            return False
 
        font_system = register_fonts()
        if font_system == 'NotoSansTamil':
            base_font = 'NotoSansTamil'
            bold_font = 'NotoSansTamil-Bold'
        elif font_system == 'DejaVuSans':
            base_font = 'DejaVuSans'
            bold_font = 'DejaVuSans-Bold'
        else:
            base_font = 'Helvetica'
            bold_font = 'Helvetica-Bold'
 
        os.makedirs(os.path.dirname(output_pdf), exist_ok=True)
 
        doc = SimpleDocTemplate(
            output_pdf,
            pagesize=letter,
            leftMargin=50,
            rightMargin=50,
            topMargin=70,
            bottomMargin=70
        )
 
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            'Question', parent=styles['Normal'], fontSize=11, leading=14, alignment=TA_JUSTIFY,
            leftIndent=20, spaceAfter=6, spaceBefore=8, fontName=base_font
        ))
        styles.add(ParagraphStyle(
            'Option', parent=styles['Normal'], fontSize=10.5, leftIndent=35,
            spaceAfter=4, leading=12, fontName=base_font
        ))
        styles.add(ParagraphStyle(
            'SectionHeader', parent=styles['Heading2'], fontSize=13,
            alignment=TA_CENTER, spaceBefore=18, spaceAfter=10,
            textColor=HexColor("#1a5276"), backColor=HexColor("#d4e6f1"), leading=16, fontName=bold_font
        ))
 
        elements = []
 
        title_style = ParagraphStyle(
            'Title', parent=styles['Title'], fontSize=15, alignment=TA_CENTER,
            spaceBefore=18, spaceAfter=25, textColor=HexColor("#1a5276"), fontName=bold_font
        )
 
        title_text = f"Question Paper"
        if language_filter:
            title_text += f" - {language_filter}"
        title_text += f" ({len(df)} Questions)"
 
        elements.append(Paragraph(title_text, title_style))
        elements.append(Spacer(1, 0.3 * inch))
 
        current_section = None
 
        for idx, row in df.iterrows():
            try:
                qno = row.get('qno', idx + 1)
 
                block = []
 
                if 'type_of_question' in row and pd.notna(row['type_of_question']):
                    question_type = str(row['type_of_question']).strip()
                    if question_type != current_section:
                        current_section = question_type
                        block.append(Spacer(1, 0.2 * inch))
                        block.append(Paragraph(f"SECTION: {question_type.upper()}", styles['SectionHeader']))
                        block.append(Spacer(1, 0.15 * inch))
 
                if 'question' in row and pd.notna(row['question']):
                    process_question_content(
                        block, row['question'], image_repo or '', qno, base_font, styles
                    )
 
                question_type = row.get('type_of_question', '')
                if is_mcq_question(question_type) and has_valid_options(row):
                    logger.info(f"Processing MCQ options for question {qno}")
                    option_cols = ['optiona', 'optionb', 'optionc', 'optiond']
                    for i, opt_col in enumerate(option_cols):
                        if opt_col in row and pd.notna(row[opt_col]) and str(row[opt_col]).strip():
                            option_data = str(row[opt_col]).strip()
                            if option_data not in ['', 'nan', 'None']:
                                process_option_content(
                                    block, row[opt_col], chr(65 + i), image_repo or '', styles
                                )
                    block.append(Spacer(1, 0.15 * inch))
                else:
                    block.append(Spacer(1, 0.2 * inch))
 
                block.append(Spacer(1, 0.25 * inch))
                elements.append(KeepTogether(block))
 
            except Exception as e:
                logger.error(f"Error processing question {qno}: {e}")
                elements.append(Paragraph(
                    f"[Error processing question {qno}]",
                    ParagraphStyle('Error', fontSize=9, textColor=red)
                ))
                elements.append(Spacer(1, 0.2 * inch))
 
        try:
            doc.build(elements, onFirstPage=draw_watermark, onLaterPages=draw_watermark)
            logger.info(f"Successfully created PDF with {len(df)} questions: {output_pdf}")
            return True
        except Exception as e:
            logger.error(f"Error building PDF: {e}")
            return False
 
    except Exception as e:
        logger.error(f"Error in create_pdf_from_excel: {e}")
        return False
def diagnose_excel_structure(excel_file):
    """Diagnose Excel file structure for JSON format"""
    print("=== EXCEL STRUCTURE DIAGNOSIS ===")
    
    if not os.path.exists(excel_file):
        print(f"Excel file does not exist: {excel_file}")
        return
    
    try:
        df = pd.read_excel(excel_file)
        print(f"Rows: {len(df)}")
        print(f"Columns: {list(df.columns)}")
        
        # Analyze question types
        if 'type_of_question' in df.columns:
            question_types = df['type_of_question'].value_counts()
            print(f"\nQuestion Types Distribution:")
            for qtype, count in question_types.items():
                print(f"  {qtype}: {count} questions")
                mcq_status = "✓ MCQ" if is_mcq_question(qtype) else "✗ Non-MCQ"
                print(f"    Status: {mcq_status}")
        
        # Check sample data
        for idx, row in df.head(3).iterrows():
            print(f"\n--- Row {idx + 1} ---")
            print(f"QNo: {row.get('qno', 'N/A')}")
            print(f"Language: {row.get('language', 'N/A')}")
            print(f"Type: {row.get('type_of_question', 'N/A')}")
            
            # Check if it should have options
            should_have_options = is_mcq_question(row.get('type_of_question', ''))
            has_options = has_valid_options(row)
            print(f"Should have options: {should_have_options}")
            print(f"Has valid options: {has_options}")
            
            # Check question format
            if 'question' in row:
                question_sample = str(row['question'])[:100] + "..." if len(str(row['question'])) > 100 else str(row['question'])
                print(f"Question: {question_sample}")
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")

if __name__ == "__main__":
    from pathlib import Path
    
    # Configuration
    input_dir = Path(r"data\output\excel")  # Input directory containing Excel files
    output_dir = Path(r"data\output\pdf")   # Output directory for PDFs
    image_repo = Path(r"data\output\excel\images")  # Image repository path
    
    # Language filter - set to specific language or None for all
    language_preference = 'English'  # Change to 'Tamil' or None as needed
    
    # Create output directory if it doesn't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Find all Excel files in the input directory
    excel_files = list(input_dir.glob("*.xlsx"))
    
    if not excel_files:
        print(f"No Excel files found in {input_dir}")
        print("Please check the directory path and ensure it contains .xlsx files")
    else:
        print(f"Found {len(excel_files)} Excel file(s) to process:")
        for file in excel_files:
            print(f"  - {file.name}")
        
        print("\n" + "="*60)
        print("PROCESSING ALL EXCEL FILES - FIXED VERSION")
        print("="*60)
        
        successful_conversions = 0
        failed_conversions = 0
        
        # Process each Excel file
        for excel_file in excel_files:
            print(f"\n📄 Processing: {excel_file.name}")
            
            # Generate output PDF name
            output_pdf = output_dir / f"{excel_file.stem}_converted.pdf"
            
            try:
                # Run diagnosis for each file
                print(f"   Running diagnosis...")
                diagnose_excel_structure(str(excel_file))
                
                # Create PDF
                print(f"   Converting to PDF...")
                success = create_pdf_from_excel(
                    str(excel_file), 
                    str(output_pdf), 
                    language_preference, 
                    str(image_repo)
                )
                
                if success:
                    print(f"   ✓ PDF created successfully: {output_pdf.name}")
                    successful_conversions += 1
                else:
                    print(f"   ✗ Failed to create PDF for {excel_file.name}")
                    failed_conversions += 1
                    
            except Exception as e:
                print(f"   ✗ Error processing {excel_file.name}: {str(e)}")
                failed_conversions += 1
        
        # Summary
        print("\n" + "="*60)
        print("CONVERSION SUMMARY")
        print("="*60)
        print(f"Total files processed: {len(excel_files)}")
        print(f"Successful conversions: {successful_conversions}")
        print(f"Failed conversions: {failed_conversions}")
        
        if successful_conversions > 0:
            print(f"\n✓ PDF files saved to: {output_dir}")
    
    print("\n" + "="*60)
    print("FIXED JSON-BASED EXCEL TO PDF CONVERTER")
    print("="*60)
    print("Key Features:")
    print("✓ Processes ALL Excel files in input directory")
    print("✓ FIXED: Only shows options for MCQ questions")
    print("✓ Handles JSON-formatted question and option data")
    print("✓ Extracts text, images, and tables from JSON structure")
    print("✓ Supports both English and Tamil content")
    print("✓ Proper image placement and scaling")
    print("✓ Language-based filtering")
    print("✓ Professional PDF formatting with sections")
    print("✓ Comprehensive error handling and logging")
    print("✓ Batch processing with detailed progress reporting")
    print("✓ Smart question type detection")
