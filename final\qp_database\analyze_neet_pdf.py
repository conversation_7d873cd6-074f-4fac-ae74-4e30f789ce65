#!/usr/bin/env python3
"""
NEET PDF Structure Analysis Tool
Analyzes the akash.pdf to understand question format, layout, and structure
"""

import fitz  # PyMuPDF
import os
import sys
from pathlib import Path
import re
from ultralytics import YOLO

def analyze_pdf_structure(pdf_path, max_pages=5):
    """Analyze PDF structure to understand question format and layout"""
    print(f"📄 Analyzing PDF: {pdf_path}")
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        return
    
    doc = fitz.open(pdf_path)
    total_pages = len(doc)
    print(f"📖 Total pages: {total_pages}")
    
    # Analyze first few pages to understand structure
    analyze_pages = min(max_pages, total_pages)
    
    for page_num in range(analyze_pages):
        print(f"\n{'='*50}")
        print(f"PAGE {page_num + 1}")
        print(f"{'='*50}")
        
        page = doc[page_num]
        
        # Get page dimensions
        rect = page.rect
        print(f"Page dimensions: {rect.width:.0f} x {rect.height:.0f}")
        
        # Extract text blocks with positions
        blocks = page.get_text("blocks")
        print(f"\nText blocks found: {len(blocks)}")
        
        question_patterns = []
        option_patterns = []
        answer_patterns = []
        branding_patterns = []
        
        for i, block in enumerate(blocks):
            x0, y0, x1, y1, text, block_no, block_type = block
            text = text.strip()
            
            if not text:
                continue
                
            # Analyze text patterns
            print(f"\nBlock {i+1}: ({x0:.0f},{y0:.0f},{x1:.0f},{y1:.0f})")
            print(f"Text: {repr(text[:100])}")
            
            # Check for question numbers
            if re.match(r'^\d+\.', text):
                question_patterns.append({
                    'block_num': i+1,
                    'position': (x0, y0, x1, y1),
                    'text': text[:50],
                    'type': 'question_start'
                })
                print("  → QUESTION START detected")
            
            # Check for options (A), (B), (C), (D) or (1), (2), (3), (4)
            if re.match(r'^\([A-D1-4]\)', text) or re.search(r'\([A-D1-4]\)', text):
                option_patterns.append({
                    'block_num': i+1,
                    'position': (x0, y0, x1, y1),
                    'text': text[:50],
                    'type': 'option'
                })
                print("  → OPTION detected")
            
            # Check for answers/solutions
            if re.search(r'Answer\s*\(\d+\)|Sol\.|Solution', text, re.IGNORECASE):
                answer_patterns.append({
                    'block_num': i+1,
                    'position': (x0, y0, x1, y1),
                    'text': text[:50],
                    'type': 'answer/solution'
                })
                print("  → ANSWER/SOLUTION detected (to be skipped)")
            
            # Check for branding/headers/footers
            if any(brand in text.lower() for brand in ['aakash', 'neet', 'test booklet', 'code e1', 'tower']):
                branding_patterns.append({
                    'block_num': i+1,
                    'position': (x0, y0, x1, y1),
                    'text': text[:50],
                    'type': 'branding'
                })
                print("  → BRANDING detected (to be skipped)")
        
        # Summary for this page
        print(f"\n📊 PAGE {page_num + 1} SUMMARY:")
        print(f"  Questions detected: {len(question_patterns)}")
        print(f"  Options detected: {len(option_patterns)}")
        print(f"  Answers/Solutions detected: {len(answer_patterns)}")
        print(f"  Branding elements detected: {len(branding_patterns)}")
        
        # Show question patterns
        if question_patterns:
            print(f"\n🔍 Question patterns on page {page_num + 1}:")
            for q in question_patterns:
                print(f"    {q['text']}")
    
    doc.close()
    print(f"\n✅ PDF structure analysis complete")

def analyze_images_with_yolo(pdf_path, yolo_model_path, max_pages=3):
    """Analyze images in PDF using YOLO model"""
    print(f"\n🖼️ Analyzing images with YOLO...")
    
    if not os.path.exists(yolo_model_path):
        print(f"❌ YOLO model not found: {yolo_model_path}")
        return
    
    model = YOLO(yolo_model_path)
    doc = fitz.open(pdf_path)
    
    for page_num in range(min(max_pages, len(doc))):
        print(f"\n📄 Analyzing images on page {page_num + 1}")
        
        page = doc[page_num]
        
        # Render page as image
        pix = page.get_pixmap(dpi=300)
        temp_img_path = f"temp_page_{page_num + 1}.jpg"
        pix.save(temp_img_path)
        
        # Run YOLO detection
        try:
            results = model.predict(
                source=temp_img_path,
                imgsz=640,
                conf=0.1,
                iou=0.3,
                task='detect',
                verbose=False
            )
            
            if results[0].boxes:
                boxes = results[0].boxes
                print(f"  🎯 Found {len(boxes)} image regions")
                
                for i, box in enumerate(boxes):
                    x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
                    conf = float(box.conf[0])
                    width, height = x2 - x1, y2 - y1
                    
                    print(f"    Image {i+1}: ({x1},{y1},{x2},{y2}) size:{width}x{height} conf:{conf:.2f}")
                    
                    # Analyze position to determine if it's likely a question image or option image
                    page_height = pix.height
                    relative_y = y1 / page_height
                    
                    if width > 200 and height > 100:
                        print(f"      → Likely QUESTION IMAGE (large)")
                    elif width < 150 and height < 150:
                        print(f"      → Likely OPTION IMAGE (small)")
                    else:
                        print(f"      → Medium sized image")
                    
                    if relative_y > 0.85:
                        print(f"      → Bottom of page (likely logo/footer)")
            else:
                print(f"  📭 No images detected on page {page_num + 1}")
        
        except Exception as e:
            print(f"  ❌ YOLO detection error: {e}")
        
        # Clean up temp file
        if os.path.exists(temp_img_path):
            os.remove(temp_img_path)
    
    doc.close()

def main():
    # Path to the NEET PDF
    pdf_path = Path("../../please/akash.pdf")
    yolo_model_path = Path("src/yolo/best.pt")
    
    if not pdf_path.exists():
        print(f"❌ PDF not found: {pdf_path}")
        return
    
    print("🚀 Starting NEET PDF Analysis")
    print("="*60)
    
    # Analyze PDF structure
    analyze_pdf_structure(str(pdf_path), max_pages=5)
    
    # Analyze images with YOLO
    if yolo_model_path.exists():
        analyze_images_with_yolo(str(pdf_path), str(yolo_model_path), max_pages=3)
    else:
        print(f"⚠️ YOLO model not found at {yolo_model_path}, skipping image analysis")
    
    print("\n🎯 Analysis complete! Use this information to design the extraction pipeline.")

if __name__ == "__main__":
    main()
