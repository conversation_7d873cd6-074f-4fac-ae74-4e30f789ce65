# Questions Extractor Documentation

This project automatically extracts questions from PDF files and converts them to Excel format with image processing capabilities.

## Project Structure

Based on the provided screenshot, the project is organized as follows:

```
Questions Extractor/
├── data/
│   ├── input/test/           # Place your PDF files here
│   └── output/
│       ├── excel/            # Generated Excel files
│       └── pdf/              # Generated PDF files
├── src/
│   ├── config/
│   │   └── config.json       # Configuration settings
│   ├── yolo/
│   │   └── best.pt          # AI model for image detection
│   ├── base.py              # Core processing logic
│   └── extractor.py         # Main extraction class
├── app.py                   # Main application entry point
├── convertToPDF.py          # Excel to PDF converter
└── requirements.txt         # Required Python packages
```

## What This Project Does

1. **Reads PDF question papers** from the input folder
2. **Extracts questions and answers** using AI (Google Gemini)
3. **Finds and saves images** from the PDFs
4. **Creates Excel files** with structured question data
5. **Converts Excel back to PDF** format if needed

## Setup Instructions

### Step 1: Create Virtual Environment
```bash
# Create a new virtual environment
python -m venv venv

# Activate the virtual environment
# On Windows:
venv\Scripts\activate

# On macOS/Linux:
source venv/bin/activate
```

### Step 2: Install Requirements
```bash
pip install -r requirements.txt
```

### Step 3: Get API Key
1. Go to Google AI Studio
2. Create a new API key for Gemini
3. Create a `.env` file in the project root
4. Add your API key:
```
GEMINI_API_KEY=your_api_key_here
```

### Step 4: Configure Settings
Edit `src/config/config.json`:
```json
{
    "input_dir": "data/input/test",
    "output_excel_dir": "data/output/excel", 
    "output_image_dir": "data/output/excel/images"
}
```

## How to Use

### Extract Questions from PDF
1. **Activate virtual environment** (if not already active):
   ```bash
   # Windows:
   venv\Scripts\activate
   
   # macOS/Linux:
   source venv/bin/activate
   ```

2. Place your PDF files in `data/input/test/` folder

3. Run the main application:
   ```bash
   python app.py
   ```

4. The program will:
   - Process all PDF files in the input folder
   - Extract questions, options, and images
   - Save Excel files in `data/output/excel/`
   - Save extracted images in `data/output/excel/images/`

### Convert Excel to PDF
1. After extraction, run the converter:
   ```bash
   python convertToPDF.py
   ```
2. This creates formatted PDF question papers from the Excel data

## Output Format

### Excel Structure
Each Excel file contains columns:
- **qno**: Question number
- **language**: English or Tamil
- **question**: Question text with LaTeX math (JSON format)
- **optiona/b/c/d**: Answer options (JSON format)
- **type_of_question**: MCQ, Short Answer, etc.
- **mark**: Points for the question
- **blooms_level**: Cognitive level (Understanding, Applying, etc.)

### JSON Format Example
Questions and options are stored as JSON:
```json
{
    "text": "Find the value of $$x^2 + y^2$$",
    "image": ["diagram_image.jpg"],
    "tables": {}
}
```

## Key Features

**Smart Image Detection**: Uses YOLO AI to find images in PDFs and distinguishes between actual images and mathematical formulas

**Bilingual Support**: Handles both English and Tamil text correctly

**Mathematical Content**: Converts mathematical expressions to LaTeX format using `$$` delimiters

**Duplicate Prevention**: Avoids saving the same image multiple times

**Batch Processing**: Processes multiple PDF files automatically

**Professional Output**: Creates well-formatted Excel and PDF files

## File Naming Convention

Images are saved with descriptive names:
- `filename_Q1_English_diagram_page2.jpg`
- `filename_Q2_Tamil_optionA_page3.jpg`

## How it works
![flow_diagram](https://github.com/user-attachments/assets/d39b7dfe-7785-4308-b581-00de61e3fd92)


## Troubleshooting

**Virtual environment issues**: Make sure to activate the virtual environment before running any commands

**No questions extracted**: Check if your PDF has text (not just scanned images)

**Missing images**: Ensure images in PDF are clear and not too small

**API errors**: Verify your Gemini API key is correct and has quota remaining

**File not found**: Check that input/output directories exist and have proper permissions

**Package installation errors**: Make sure you're in the activated virtual environment when installing requirements
