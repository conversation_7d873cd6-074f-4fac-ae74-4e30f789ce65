"""
Modern PDF to High-Quality Image Converter
Converts PDF pages to 300-400 DPI images for AI document understanding
"""

import logging
from pathlib import Path
from typing import List, Optional
import fitz  # PyMuPDF
from pdf2image import convert_from_path
from PIL import Image
import numpy as np
import io
from tqdm import tqdm

try:
    from .config import PipelineConfig
except ImportError:
    from config import PipelineConfig

class PDFToImageConverter:
    """Convert PDF pages to high-quality images for AI processing"""
    
    def __init__(self, pdf_path: str, config: PipelineConfig = None):
        self.pdf_path = Path(pdf_path)
        self.config = config or PipelineConfig()
        self.logger = self._setup_logger()
        
        # Ensure PDF exists
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF not found: {self.pdf_path}")
    
    def _setup_logger(self):
        """Setup logging for the converter"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def convert_pages_to_images(self, max_pages: int = None) -> List[Path]:
        """
        Convert PDF pages to high-quality images
        
        Args:
            max_pages: Maximum number of pages to process (default: config.MAX_PAGES)
            
        Returns:
            List of paths to generated images
        """
        max_pages = max_pages or self.config.MAX_PAGES
        self.logger.info(f"Converting first {max_pages} pages of {self.pdf_path.name}")
        
        # Setup directories
        self.config.setup_directories()
        
        try:
            # Method 1: Try pdf2image (better quality)
            return self._convert_with_pdf2image(max_pages)
        except Exception as e:
            self.logger.warning(f"pdf2image failed: {e}")
            self.logger.info("Falling back to PyMuPDF...")
            
            # Method 2: Fallback to PyMuPDF
            return self._convert_with_pymupdf(max_pages)
    
    def _convert_with_pdf2image(self, max_pages: int) -> List[Path]:
        """Convert using pdf2image (preferred method)"""
        self.logger.info("Using pdf2image for high-quality conversion...")
        
        # Convert PDF to images
        images = convert_from_path(
            str(self.pdf_path),
            dpi=self.config.PDF_DPI,
            first_page=1,
            last_page=max_pages,
            fmt=self.config.IMAGE_FORMAT.lower()
        )
        
        image_paths = []
        
        for page_num, image in enumerate(tqdm(images, desc="Saving images"), 1):
            # Get output path
            image_path = self.config.get_temp_image_path(page_num)
            
            # Save image
            image.save(image_path, self.config.IMAGE_FORMAT, quality=95, optimize=True)
            image_paths.append(image_path)
            
            self.logger.debug(f"Saved page {page_num} to {image_path}")
        
        self.logger.info(f"Successfully converted {len(image_paths)} pages using pdf2image")
        return image_paths
    
    def _convert_with_pymupdf(self, max_pages: int) -> List[Path]:
        """Convert using PyMuPDF (fallback method)"""
        self.logger.info("Using PyMuPDF for conversion...")
        
        doc = fitz.open(str(self.pdf_path))
        image_paths = []
        
        # Calculate zoom for desired DPI
        zoom = self.config.PDF_DPI / 72.0  # 72 DPI is default
        mat = fitz.Matrix(zoom, zoom)
        
        for page_num in tqdm(range(min(max_pages, len(doc))), desc="Converting pages"):
            page = doc[page_num]
            
            # Render page to image
            pix = page.get_pixmap(matrix=mat)
            
            # Convert to PIL Image
            img_data = pix.tobytes("png")
            image = Image.open(io.BytesIO(img_data))
            
            # Get output path
            image_path = self.config.get_temp_image_path(page_num + 1)
            
            # Save image
            image.save(image_path, self.config.IMAGE_FORMAT, quality=95, optimize=True)
            image_paths.append(image_path)
            
            self.logger.debug(f"Saved page {page_num + 1} to {image_path}")
        
        doc.close()
        self.logger.info(f"Successfully converted {len(image_paths)} pages using PyMuPDF")
        return image_paths
    
    def get_page_info(self) -> dict:
        """Get information about the PDF"""
        doc = fitz.open(str(self.pdf_path))
        
        info = {
            "total_pages": len(doc),
            "title": doc.metadata.get("title", ""),
            "author": doc.metadata.get("author", ""),
            "subject": doc.metadata.get("subject", ""),
            "file_size_mb": self.pdf_path.stat().st_size / (1024 * 1024)
        }
        
        doc.close()
        return info
    
    def cleanup_temp_images(self):
        """Remove temporary images"""
        temp_dir = self.config.TEMP_DIR
        if temp_dir.exists():
            for img_file in temp_dir.glob("*.png"):
                img_file.unlink()
            self.logger.info("Cleaned up temporary images")


def test_converter():
    """Test the PDF converter"""
    config = PipelineConfig()
    
    # Test with akash.pdf
    converter = PDFToImageConverter(config.INPUT_PDF, config)
    
    # Get PDF info
    info = converter.get_page_info()
    print(f"PDF Info: {info}")
    
    # Convert first 3 pages for testing
    image_paths = converter.convert_pages_to_images(max_pages=3)
    print(f"Converted {len(image_paths)} pages")
    
    for path in image_paths:
        print(f"  - {path}")


if __name__ == "__main__":
    test_converter()
