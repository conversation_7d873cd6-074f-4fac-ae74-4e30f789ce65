{% extends "base.html" %} {% block title %}Question Viewer - All Questions{%
endblock %} {% block content %}
<div class="header">
  <h1>📚 Question Paper Viewer</h1>
  <p>Extracted Questions from PDF with Image Support</p>
</div>

<div class="stats">
  <div class="stat-item">
    <div class="stat-number">{{ stats.total_questions }}</div>
    <div class="stat-label">Total Questions</div>
  </div>
  <div class="stat-item">
    <div class="stat-number">{{ stats.questions_with_images }}</div>
    <div class="stat-label">With Images</div>
  </div>
  <div class="stat-item">
    <div class="stat-number">
      Q{{ stats.question_numbers|min }} - Q{{ stats.question_numbers|max }}
    </div>
    <div class="stat-label">Question Range</div>
  </div>
</div>

<div class="navigation">
  <h3 style="margin-bottom: 15px">Quick Navigation</h3>
  {% for qno in stats.question_numbers %}
  <a
    href="/question/{{ qno }}"
    class="nav-button"
    style="margin: 5px; padding: 8px 12px; font-size: 0.9em"
    >Q{{ qno }}</a
  >
  {% endfor %}
</div>

{% for question in questions %}
<div class="question-card">
  <div class="question-header">
    <div class="question-number">Q{{ question.qno }}</div>
    <div class="question-meta">
      <span class="language-tag">{{ question.language }}</span>
      {% if question.has_image %}
      <span class="image-indicator">📷 Has Image</span>
      {% endif %}
    </div>
  </div>

  <div class="question-text">
    {% if question.image_files %} {% for image_file in question.image_files %}
    <div class="image-container">
      <img
        src="/images/{{ image_file }}"
        alt="Question Image"
        class="question-image"
        onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
      />
      <div
        class="image-error"
        style="
          display: none;
          padding: 10px;
          background: #f0f0f0;
          border: 1px dashed #ccc;
          text-align: center;
        "
      >
        📷 Image: {{ image_file }}
      </div>
    </div>
    {% endfor %} {% endif %} {{ question.clean_question if
    question.clean_question else question.question|safe }}
  </div>

  <div class="options">
    <div class="option">
      <div class="option-label">A)</div>
      <div>{{ question.optiona }}</div>
    </div>
    <div class="option">
      <div class="option-label">B)</div>
      <div>{{ question.optionb }}</div>
    </div>
    <div class="option">
      <div class="option-label">C)</div>
      <div>{{ question.optionc }}</div>
    </div>
    <div class="option">
      <div class="option-label">D)</div>
      <div>{{ question.optiond }}</div>
    </div>
  </div>

  <div style="text-align: center; margin-top: 20px">
    <a href="/question/{{ question.qno }}" class="nav-button"
      >View Full Question</a
    >
  </div>
</div>
{% endfor %}

<div class="navigation">
  <a href="#" onclick="window.scrollTo(0,0)" class="nav-button"
    >🔝 Back to Top</a
  >
</div>

<script>
  // Add some interactivity
  document.addEventListener("DOMContentLoaded", function () {
    // Add click handlers for question cards
    const questionCards = document.querySelectorAll(".question-card");
    questionCards.forEach((card) => {
      card.addEventListener("click", function (e) {
        if (e.target.tagName !== "A" && e.target.tagName !== "BUTTON") {
          const questionNumber = this.querySelector(
            ".question-number"
          ).textContent.replace("Q", "");
          window.location.href = `/question/${questionNumber}`;
        }
      });
    });

    // Add keyboard navigation
    document.addEventListener("keydown", function (e) {
      if (e.key === "Home") {
        window.scrollTo(0, 0);
      } else if (e.key === "End") {
        window.scrollTo(0, document.body.scrollHeight);
      }
    });
  });
</script>
{% endblock %}
