import fitz
import os
import re
import pandas as pd
from pathlib import Path
from ultralytics import Y<PERSON><PERSON>

def is_logo(box, page_width, page_height):
    x1, y1, x2, y2 = box
    width, height = x2 - x1, y2 - y1
    is_bottom = y1 > page_height * 0.85
    is_right = x1 > page_width * 0.7
    is_small = width < 50 or height < 50
    aspect_ratio = width / height if height > 0 else 0
    is_logo_aspect = 0.8 < aspect_ratio < 1.2  # nearly square
    is_very_wide = aspect_ratio > 3
    return (is_bottom and is_right) or is_small or is_logo_aspect or is_very_wide

def detect_images(pdf_path, start_page, end_page, images_dir):
    model = YOLO(str(Path(__file__).parent / "../../qp_database/src/yolo/best.pt"))
    doc = fitz.open(pdf_path)
    image_info = []
    os.makedirs(images_dir, exist_ok=True)
    for page_num in range(start_page, end_page + 1):
        page = doc[page_num - 1]
        pix = page.get_pixmap(dpi=300)
        img_path = os.path.join(images_dir, f"page_{page_num}.jpg")
        pix.save(img_path)
        boxes = model.predict(source=img_path, imgsz=640, conf=0.01, iou=0.1, task='detect', verbose=False)[0].boxes
        for idx, box in enumerate(boxes):
            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
            width, height = x2 - x1, y2 - y1
            if is_logo((x1, y1, x2, y2), pix.width, pix.height):
                continue
            # Only crop if region is valid and within page bounds
            if width <= 0 or height <= 0:
                continue
            x1_clip = max(0, min(x1, pix.width))
            y1_clip = max(0, min(y1, pix.height))
            x2_clip = max(0, min(x2, pix.width))
            y2_clip = max(0, min(y2, pix.height))
            if x2_clip <= x1_clip or y2_clip <= y1_clip:
                continue
            try:
                crop = page.get_pixmap(dpi=300, clip=(x1_clip, y1_clip, x2_clip, y2_clip))
                crop_path = os.path.join(images_dir, f"Qimg_page{page_num}_{idx}.jpg")
                crop.save(crop_path)
                image_info.append({
                    'page': page_num,
                    'y_mid': (y1 + y2) // 2,
                    'filename': os.path.basename(crop_path),
                    'path': crop_path,
                    'bbox': (x1, y1, x2, y2)
                })
            except Exception as e:
                print(f"Skipping invalid crop on page {page_num}, box {idx}: {e}")
                continue
    doc.close()
    return image_info

def extract_blocks(pdf_path, start_page, end_page):
    doc = fitz.open(pdf_path)
    blocks = []
    for page_num in range(start_page, end_page + 1):
        page = doc[page_num - 1]
        for block in page.get_text("blocks"):
            x0, y0, x1, y1, text, *_ = block
            if text.strip():
                blocks.append({'page': page_num, 'y_mid': int((y0 + y1) // 2), 'y0': int(y0), 'y1': int(y1), 'text': text.strip()})
    doc.close()
    return blocks

def group_blocks_to_questions(blocks):
    questions = []
    current = None
    for block in blocks:
        text = block['text']
        # Start of a new question (e.g., '1.' or '2.')
        m = re.match(r'^(\d+)\.\s*(.*)', text)
        if m:
            if current:
                questions.append(current)
            current = {'stem': m.group(2), 'options': {}, 'page': block['page'], 'y_mid': block['y_mid'], 'block_y0': block['y0'], 'block_y1': block['y1']}
            continue
        # Option blocks
        mopt = re.match(r'^\(?([A-D])\)?[\).\s]+(.*)', text)
        if mopt and current:
            opt = mopt.group(1)
            if opt:
                current['options'][opt] = mopt.group(2).strip()
            continue
        # If not a new question or option, append to stem
        if current:
            current['stem'] += ' ' + text
    if current:
        questions.append(current)
    return questions

def map_images_to_questions(questions, image_info):
    for q in questions:
        # Find images on the same page, whose y_mid is between stem and last option, or just after
        q_page = q['page']
        q_y0 = q['block_y0']
        q_y1 = q['block_y1']
        option_ys = [q_y1]
        for opt in q['options'].values():
            # Option blocks are not tracked by y, so just use stem for now
            pass
        mapped = []
        for img in image_info:
            if img['page'] == q_page and img['y_mid'] >= q_y0 and img['y_mid'] <= q_y0 + 500:  # 500px after stem
                mapped.append(img['filename'])
        q['images'] = mapped
    return questions

def build_output_rows(questions):
    rows = []
    for q in questions:
        row = {
            'question': q['stem'],
            'optiona': q['options'].get('A', ''),
            'optionb': q['options'].get('B', ''),
            'optionc': q['options'].get('C', ''),
            'optiond': q['options'].get('D', ''),
            'image': ', '.join(q['images']) if q.get('images') else ''
        }
        # Insert image placeholder in question text if image mapped
        if row['image']:
            row['question'] = f"<image:{row['image']}> " + row['question']
        rows.append(row)
    return rows

def save_questions_to_excel(rows, excel_path):
    df = pd.DataFrame(rows)
    df = df[['question', 'optiona', 'optionb', 'optionc', 'optiond', 'image']]
    df.to_excel(excel_path, index=False)

def main():
    pdf_path = Path("C:/varala/qp_database/data/input/clgdunia.pdf")
    images_dir = Path("C:/varala/qp_database/data/output/excel2/images/")
    output_excel = Path("C:/varala/qp_database/data/output/excel2/clgdunia_questions_with_images.xlsx")
    # Detect images
    image_info = detect_images(pdf_path, 2, 11, images_dir)
    # Extract text blocks
    blocks = extract_blocks(pdf_path, 2, 11)
    # Group blocks into questions
    questions = group_blocks_to_questions(blocks)
    # Map images to questions
    questions = map_images_to_questions(questions, image_info)
    # Build output rows
    rows = build_output_rows(questions)
    rows = rows[:30]
    save_questions_to_excel(rows, output_excel)
    print(f"Extracted {len(rows)} questions to {output_excel}")

if __name__ == "__main__":
    main() 