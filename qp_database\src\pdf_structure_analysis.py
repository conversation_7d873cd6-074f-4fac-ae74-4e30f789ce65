import fitz
from pathlib import Path
from ultralytics import Y<PERSON><PERSON>

def is_logo(box, page_width, page_height):
    x1, y1, x2, y2 = box
    width, height = x2 - x1, y2 - y1
    is_bottom = y1 > page_height * 0.85
    is_right = x1 > page_width * 0.7
    is_small = width < 50 or height < 50
    aspect_ratio = width / height if height > 0 else 0
    is_logo_aspect = 0.8 < aspect_ratio < 1.2  # nearly square
    is_very_wide = aspect_ratio > 3
    return (is_bottom and is_right) or is_small or is_logo_aspect or is_very_wide

def analyze_pdf_structure(pdf_path, yolo_model_path, start_page=2, end_page=4):
    doc = fitz.open(pdf_path)
    model = YOLO(str(yolo_model_path))
    for page_num in range(start_page, end_page + 1):
        print(f"\n=== PAGE {page_num} ===")
        page = doc[page_num - 1]
        # Print all text blocks with positions
        print("Text blocks:")
        for block in page.get_text("blocks"):
            x0, y0, x1, y1, text, *_ = block
            if text.strip():
                print(f"  Block: ({x0:.0f},{y0:.0f},{x1:.0f},{y1:.0f}) | {repr(text.strip())[:80]}")
        # Render page as image for YOLO
        pix = page.get_pixmap(dpi=300)
        img_path = f"temp_page_{page_num}.jpg"
        pix.save(img_path)
        boxes = model.predict(source=img_path, imgsz=640, conf=0.01, iou=0.1, task='detect', verbose=False)[0].boxes
        print("Detected images (YOLO):")
        for idx, box in enumerate(boxes):
            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
            logo_flag = is_logo((x1, y1, x2, y2), pix.width, pix.height)
            print(f"  Box {idx}: ({x1},{y1},{x2},{y2}) | {'LOGO' if logo_flag else 'CONTENT'}")
        # Clean up temp image
        Path(img_path).unlink(missing_ok=True)
    doc.close()

def main():
    pdf_path = Path("C:/varala/qp_database/data/input/clgdunia.pdf")
    yolo_model_path = Path("C:/varala/qp_database/src/yolo/best.pt")
    analyze_pdf_structure(pdf_path, yolo_model_path, 2, 4)

if __name__ == "__main__":
    main() 