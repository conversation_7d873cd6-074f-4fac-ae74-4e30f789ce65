#!/usr/bin/env python3
"""
Display the extracted questions and their mapped images
"""

import pandas as pd
import os
from pathlib import Path

def display_results():
    """Display the extraction results in a formatted way."""
    
    # Find the most recent Excel file
    excel_dir = Path("data/output/excel")
    excel_files = list(excel_dir.glob("*.xlsx"))
    
    if not excel_files:
        print("❌ No Excel files found!")
        return
    
    # Get the most recent file
    latest_file = max(excel_files, key=os.path.getctime)
    print(f"📊 Reading results from: {latest_file.name}")
    print("=" * 80)
    
    # Read the Excel file
    try:
        df = pd.read_excel(latest_file)
        print(f"📝 Total Questions Extracted: {len(df)}")
        print("=" * 80)
        
        # Count questions with images
        questions_with_images = 0
        questions_text_only = 0
        
        print("\n🔍 QUESTION ANALYSIS:")
        print("-" * 50)
        
        for i, row in df.iterrows():
            qno = row['qno']
            question = str(row['question'])
            
            # Check if question has an image
            has_image = '.jpg' in question or '.png' in question
            
            if has_image:
                questions_with_images += 1
                # Extract image filename
                words = question.split()
                image_file = None
                for word in words:
                    if '.jpg' in word or '.png' in word:
                        image_file = word
                        break
                
                print(f"📸 Q{qno}: HAS IMAGE - {image_file}")
                # Show question text (first 100 chars)
                clean_question = question.replace(image_file, '').strip()
                print(f"   Text: {clean_question[:100]}...")
                print()
            else:
                questions_text_only += 1
                if i < 10:  # Show first 10 text-only questions
                    print(f"📝 Q{qno}: TEXT ONLY")
                    print(f"   Text: {question[:100]}...")
                    print()
        
        print("=" * 80)
        print("📊 SUMMARY:")
        print(f"   📸 Questions with Images: {questions_with_images}")
        print(f"   📝 Text-only Questions: {questions_text_only}")
        print(f"   📋 Total Questions: {len(df)}")
        print("=" * 80)
        
        # Show image files
        images_dir = Path("data/output/excel/images")
        if images_dir.exists():
            image_files = list(images_dir.glob("Q*.jpg"))
            print(f"\n📸 EXTRACTED IMAGE FILES ({len(image_files)}):")
            print("-" * 50)
            for img_file in sorted(image_files):
                print(f"   ✅ {img_file.name}")
        
        print("\n🎉 EXTRACTION COMPLETED SUCCESSFULLY!")
        print("🌐 Open http://localhost:5000 in your browser to view the questions")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")

if __name__ == "__main__":
    display_results()
